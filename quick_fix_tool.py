#!/usr/bin/env python3
"""
Quick Fix Tool using your API configuration

This is a command-line tool that uses your API to quickly generate patches
for code issues.

Usage:
    python quick_fix_tool.py --problem "Fix the bug" --files file1.py file2.py
    python quick_fix_tool.py --interactive
"""

import argparse
import os
import sys
from your_api_config import create_your_api_generator, quick_fix


def read_files(file_paths):
    """Read multiple files and return as dictionary"""
    file_contents = {}
    
    for file_path in file_paths:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                file_contents[file_path] = f.read()
            print(f"✓ Read {file_path} ({len(file_contents[file_path])} chars)")
        else:
            print(f"✗ File not found: {file_path}")
            return None
    
    return file_contents


def interactive_mode():
    """Interactive mode for creating fixes"""
    print("="*60)
    print("INTERACTIVE QUICK FIX MODE")
    print("="*60)
    
    # Get problem description
    print("\n1. Describe the problem you want to fix:")
    problem = input("> ").strip()
    
    if not problem:
        print("Error: Problem description is required")
        return
    
    # Get files
    print("\n2. Enter file paths (one per line, empty line to finish):")
    file_paths = []
    while True:
        file_path = input("> ").strip()
        if not file_path:
            break
        file_paths.append(file_path)
    
    if not file_paths:
        print("Error: At least one file is required")
        return
    
    # Read files
    print(f"\n3. Reading {len(file_paths)} files...")
    file_contents = read_files(file_paths)
    
    if not file_contents:
        return
    
    # Choose format
    print("\n4. Choose output format:")
    print("   1. SEARCH/REPLACE format (recommended)")
    print("   2. edit_file format")
    
    format_choice = input("> ").strip()
    diff_format = format_choice != "2"
    
    # Generate fix
    print(f"\n5. Generating fix using your API...")
    print("   This may take a few seconds...")
    
    try:
        result = quick_fix(problem, file_contents, diff_format=diff_format)
        
        if result['success']:
            print("\n✓ Fix generated successfully!")
            print(f"Edited files: {result['edited_files']}")
            
            # Show patch
            print("\n" + "="*60)
            print("GENERATED PATCH:")
            print("="*60)
            print(result['model_patch'])
            
            # Ask to save
            save_choice = input("\nSave patch to file? (y/N): ").strip().lower()
            if save_choice in ['y', 'yes']:
                patch_file = input("Enter filename (default: fix.patch): ").strip()
                if not patch_file:
                    patch_file = "fix.patch"
                
                with open(patch_file, 'w') as f:
                    f.write(result['model_patch'])
                print(f"✓ Patch saved to {patch_file}")
                
                print(f"\nTo apply the patch, run:")
                print(f"  git apply {patch_file}")
        
        else:
            print(f"\n✗ Fix generation failed: {result['error']}")
            if 'raw_output' in result:
                print(f"LLM output: {result['raw_output'][:200]}...")
    
    except Exception as e:
        print(f"\n✗ Error: {e}")
        import traceback
        traceback.print_exc()


def batch_mode(problem, file_paths, output_file=None, format_type="search_replace"):
    """Batch mode for processing files"""
    print("="*60)
    print("BATCH QUICK FIX MODE")
    print("="*60)
    
    print(f"Problem: {problem}")
    print(f"Files: {', '.join(file_paths)}")
    
    # Read files
    file_contents = read_files(file_paths)
    if not file_contents:
        return False
    
    # Generate fix
    print(f"\nGenerating fix using your API...")
    diff_format = format_type == "search_replace"
    
    try:
        result = quick_fix(problem, file_contents, diff_format=diff_format)
        
        if result['success']:
            print("\n✓ Fix generated successfully!")
            print(f"Edited files: {result['edited_files']}")
            
            # Output patch
            if output_file:
                with open(output_file, 'w') as f:
                    f.write(result['model_patch'])
                print(f"✓ Patch saved to {output_file}")
            else:
                print("\n" + "="*60)
                print("GENERATED PATCH:")
                print("="*60)
                print(result['model_patch'])
            
            return True
        
        else:
            print(f"\n✗ Fix generation failed: {result['error']}")
            return False
    
    except Exception as e:
        print(f"\n✗ Error: {e}")
        return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Quick Fix Tool using your API configuration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Interactive mode
  python quick_fix_tool.py --interactive
  
  # Batch mode
  python quick_fix_tool.py --problem "Fix division by zero" --files calc.py main.py
  
  # Save to file
  python quick_fix_tool.py --problem "Add error handling" --files api.py --output fix.patch
  
  # Use edit_file format
  python quick_fix_tool.py --problem "Fix bug" --files code.py --format edit_file
        """
    )
    
    parser.add_argument('--interactive', '-i', action='store_true',
                       help='Run in interactive mode')
    parser.add_argument('--problem', '-p', type=str,
                       help='Problem description')
    parser.add_argument('--files', '-f', nargs='+',
                       help='Files to process')
    parser.add_argument('--output', '-o', type=str,
                       help='Output patch file')
    parser.add_argument('--format', choices=['search_replace', 'edit_file'],
                       default='search_replace',
                       help='Output format (default: search_replace)')
    
    args = parser.parse_args()
    
    # Show configuration
    print("Quick Fix Tool")
    print("=" * 30)
    print("API: https://aigc.sankuai.com/v1/openai/native")
    print("Model: anthropic.claude-sonnet-4")
    print("Rate Limit: 50 requests/minute")
    
    if args.interactive:
        interactive_mode()
    elif args.problem and args.files:
        success = batch_mode(args.problem, args.files, args.output, args.format)
        sys.exit(0 if success else 1)
    else:
        parser.print_help()
        print("\nError: Either use --interactive or provide --problem and --files")
        sys.exit(1)


if __name__ == "__main__":
    main()
