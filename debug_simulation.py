#!/usr/bin/env python3
"""
Debug script for simulation issues
"""

from agentless_multifile_patch import AgentlessMultifilePatchGenerator


def debug_simple_case():
    """Debug a simple case step by step"""
    print("="*60)
    print("DEBUG: Simple Case")
    print("="*60)
    
    generator = AgentlessMultifilePatchGenerator(
        model_name="gpt-4",
        llm_provider="openai",
        api_key=None,  # Force simulation
        temperature=0.0
    )
    
    problem = "Fix the function to return a greeting instead of None"
    file_contents = {
        "greetings.py": """def greet():
    return None  # Bug: should return greeting"""
    }
    
    print("Original file content:")
    print(repr(file_contents["greetings.py"]))
    print()
    
    # Step 1: Construct context
    topn_content, file_loc_intervals = generator.construct_multifile_context(file_contents)
    print("Constructed context:")
    print(repr(topn_content))
    print()
    
    # Step 2: Format message
    message = generator.repair_prompt_combine_topn_cot_diff.format(
        repair_relevant_file_instruction=generator.repair_relevant_file_instruction,
        problem_statement=problem,
        content=topn_content.rstrip()
    ).strip()
    
    print("Message to LLM (first 500 chars):")
    print(message[:500])
    print("...")
    print()
    
    # Step 3: Call LLM (simulation)
    llm_output = generator._call_llm(message, diff_format=True)
    print("LLM output:")
    print(repr(llm_output))
    print()
    
    # Step 4: Extract commands
    python_blocks = generator.extract_python_blocks(llm_output)
    print("Extracted Python blocks:")
    for i, block in enumerate(python_blocks):
        print(f"Block {i+1}:")
        print(repr(block))
        print()
    
    # Step 5: Parse commands
    if python_blocks:
        file_to_commands = generator.split_edit_multifile_commands(
            python_blocks, diff_format=True
        )
        print("File to commands:")
        for file_path, commands in file_to_commands.items():
            print(f"{file_path}: {commands}")
        print()
        
        # Step 6: Apply edits
        if "'greetings.py'" in file_to_commands:
            commands = file_to_commands["'greetings.py'"]
            original_content = file_contents["greetings.py"]
            print("Original content:")
            print(repr(original_content))
            
            new_content = generator.parse_search_replace_commands(commands, original_content)
            print("New content:")
            print(repr(new_content))
            
            print("Content changed:", original_content != new_content)


if __name__ == "__main__":
    debug_simple_case()
