#!/usr/bin/env python3
"""
Test script with realistic file content to verify the Git diff format.
"""

from agentless_multifile_patch import AgentlessMultifilePatchGenerator

def test_realistic_format():
    """Test with realistic file content that matches the expected line numbers."""
    
    # Create generator without LLM (will use simulation)
    generator = AgentlessMultifilePatchGenerator()
    
    # Create realistic file content with proper line numbers
    # This simulates the actual astropy/modeling/separable.py file structure
    lines_before = [f"# Line {i}" for i in range(1, 237)]  # Lines 1-236
    
    original_content = '\n'.join(lines_before) + '''
def _cstack(left, right):
    """
    Function corresponding to the stack operation.
    """
    noutp = left.shape[0] + right.shape[0]
    cleft = _coord_matrix(left, 'left', noutp)
    if right.shape[0] == noutp:
        cright = _coord_matrix(right, 'right', noutp)
    else:
        cright = np.zeros((noutp, right.shape[1]))
        cright[-right.shape[0]:, -right.shape[1]:] = 1

    return np.hstack([cleft, cright])
'''

    new_content = '\n'.join(lines_before) + '''
def _cstack(left, right):
    """
    Function corresponding to the stack operation.
    """
    noutp = left.shape[0] + right.shape[0]
    cleft = _coord_matrix(left, 'left', noutp)
    if right.shape[0] == noutp:
        cright = _coord_matrix(right, 'right', noutp)
    else:
        cright = np.zeros((noutp, right.shape[1]))
        cright[-right.shape[0]:, -right.shape[1]:] = right

    return np.hstack([cleft, cright])
'''

    # Test file changes
    file_changes = {
        "separable.py": (original_content, new_content)
    }
    
    # Generate diff with module prefix
    diff = generator.create_git_diff(file_changes, module_prefix="astropy/modeling")
    
    print("Generated Git diff:")
    print("=" * 60)
    print(diff)
    print("=" * 60)
    
    # Check for expected patterns
    expected_patterns = [
        "diff --git a/astropy/modeling/separable.py b/astropy/modeling/separable.py",
        "--- a/astropy/modeling/separable.py",
        "+++ b/astropy/modeling/separable.py",
        "@@ -242,7 +242,7 @@",  # This should now be correct
        "def _cstack(left, right):",
        "-        cright[-right.shape[0]:, -right.shape[1]:] = 1",
        "+        cright[-right.shape[0]:, -right.shape[1]:] = right"
    ]
    
    print("\nChecking expected patterns:")
    success = True
    for pattern in expected_patterns:
        if pattern in diff:
            print(f"✅ Found: {pattern}")
        else:
            print(f"❌ Missing: {pattern}")
            success = False
    
    if success:
        print("\n🎉 Git diff format matches expected output!")
    else:
        print("\n❌ Git diff format still has issues!")
    
    return success

if __name__ == "__main__":
    test_realistic_format()
