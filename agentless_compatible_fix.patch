diff --git a/calculator.py b/calculator.py
index 5bc4786..7172b1a 100644
--- a/calculator.py
+++ b/calculator.py
@@ -6,7 +6,9 @@ def subtract(a, b):
     return a - b
 
 def divide(a, b):
-    return a / b  # Line 8: Bug here
+    if b == 0:
+        raise ZeroDivisionError("Cannot divide by zero")
+    return a / b
 
 def multiply(a, b):
     return a * b
diff --git a/main.py b/main.py
index f354be4..a12089a 100644
--- a/main.py
+++ b/main.py
@@ -9,9 +9,13 @@ def process_numbers(a, b, operation):
     return None
 
 def main():
-    result = divide(10, 0)  # Lines 12-15: Missing exception handling
-    print(f"Result: {result}")
-    return result
+    try:
+        result = divide(10, 0)  # Lines 12-15: Missing exception handling
+        print(f"Result: {result}")
+        return result
+    except ZeroDivisionError as e:
+        print(f"Error: {e}")
+        return None
 
 if __name__ == "__main__":
     main()
\ No newline at end of file
diff --git a/utils.py b/utils.py
index deea07b..d0e0030 100644
--- a/utils.py
+++ b/utils.py
@@ -1,8 +1,15 @@
 # Utility functions
 import re
+import math
 
 def validate_input(value):
-    return value is not None  # Line 5: Weak validation
+    if value is None:
+        return False
+    if isinstance(value, str):
+        return value.strip() != ""
+    if isinstance(value, (int, float)):
+        return not (isinstance(value, float) and (math.isnan(value) or math.isinf(value)))
+    return True
 
 def sanitize_string(s):
     return s.strip() if isinstance(s, str) else str(s)
