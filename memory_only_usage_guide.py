#!/usr/bin/env python3
"""
Memory-Only Usage Guide

This script shows how to use AgentlessMultifilePatchGenerator in memory-only mode,
ensuring no files are saved to disk and the original repository remains untouched.

🔒 MEMORY-ONLY GUARANTEE:
- No files written to disk
- No temporary files created  
- Original repository untouched
- All processing in memory
- Results returned as strings
"""

from agentless_multifile_patch import AgentlessMultifilePatchGenerator


def memory_only_example():
    """Complete example of memory-only processing"""
    print("🔒 Memory-Only Processing Example")
    print("=" * 50)
    
    # 1. Initialize generator with your API
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",
        llm_provider="custom",
        api_key="1830498799983480864",
        base_url="https://aigc.sankuai.com/v1/openai/native",
        temperature=0.0,
        max_tokens=2048
    )
    
    # 2. Define problem and file contents IN MEMORY
    problem = "Fix the security vulnerabilities and add proper error handling"
    
    file_contents = {
        "auth.py": """import hashlib

def hash_password(password):
    # Security issue: MD5 is not secure
    return hashlib.md5(password.encode()).hexdigest()

def authenticate(username, password):
    # No input validation or error handling
    user = get_user(username)
    return user and user['password'] == hash_password(password)""",
        
        "main.py": """from auth import authenticate

def login(username, password):
    # No error handling
    if authenticate(username, password):
        return "success"
    return "failed"

def main():
    result = login("admin", "")  # Empty password should be rejected
    print(result)"""
    }
    
    # 3. Optionally specify locations for focused fixes
    file_locations = {
        "auth.py": [(4, 6), (8, 11)],  # hash_password and authenticate functions
        "main.py": [(4, 8)]             # login function
    }
    
    print(f"📁 Processing {len(file_contents)} files in memory...")
    
    # 4. Generate patch (ALL IN MEMORY)
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,      # ← In memory
        file_locations=file_locations,    # ← In memory  
        context_window=3,
        diff_format=True
    )
    
    # 5. Handle results (ALL IN MEMORY)
    if result['success']:
        print(f"✅ Success! Generated patch for {len(result['edited_files'])} files")
        
        # The patch is a string in memory
        patch = result['model_patch']
        print(f"📄 Patch size: {len(patch)} characters")
        
        # File changes are tuples in memory
        for file_path, (original, modified) in result['file_changes'].items():
            print(f"📝 {file_path}: {len(original)} → {len(modified)} chars")
        
        # You can return the patch string or process it further
        return patch
    else:
        print(f"❌ Failed: {result['error']}")
        return None


def read_files_from_disk_example():
    """Example: Read files from disk but process in memory only"""
    print("\n🔒 Read from Disk, Process in Memory Example")
    print("=" * 50)
    
    # Read files from your actual repository
    def read_file_safely(file_path):
        """Read file content safely"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            return None
    
    # Example: Read actual files but process in memory
    file_paths = ["example1.py", "example2.py"]  # Your actual files
    file_contents = {}
    
    for file_path in file_paths:
        content = read_file_safely(file_path)
        if content is not None:
            file_contents[file_path] = content
            print(f"📖 Read {file_path} ({len(content)} chars)")
        else:
            print(f"⚠️  File not found: {file_path}")
    
    if not file_contents:
        print("📝 Creating example files in memory instead...")
        file_contents = {
            "example.py": """def example_function():
    # This is an example function
    return "Hello World" """
        }
    
    # Process in memory (no files written)
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",
        llm_provider="custom", 
        api_key="1830498799983480864",
        base_url="https://aigc.sankuai.com/v1/openai/native"
    )
    
    result = generator.generate_multifile_patch(
        problem_statement="Add proper documentation and error handling",
        file_contents=file_contents,  # ← Read from disk, processed in memory
        diff_format=True
    )
    
    if result['success']:
        print(f"✅ Generated patch in memory (no files modified)")
        # The original files on disk are unchanged!
        return result['model_patch']
    else:
        print(f"❌ Failed: {result['error']}")
        return None


def apply_patch_manually_example():
    """Example: How to manually apply the patch if needed"""
    print("\n🔧 Manual Patch Application Example")
    print("=" * 50)
    
    # Generate patch in memory
    patch = memory_only_example()
    
    if patch:
        print(f"📋 Generated patch preview:")
        print(patch[:300] + "..." if len(patch) > 300 else patch)
        
        print(f"\n🔧 To apply this patch manually:")
        print(f"  1. Save patch to a file: echo '{patch[:50]}...' > fix.patch")
        print(f"  2. Apply with git: git apply fix.patch")
        print(f"  3. Or apply programmatically using the file_changes data")
        
        print(f"\n💡 Or use the in-memory file_changes directly:")
        print(f"  - result['file_changes'] contains (original, modified) tuples")
        print(f"  - You can write modified content to files when ready")
        print(f"  - Original files remain untouched until you decide to apply changes")


def best_practices():
    """Best practices for memory-only processing"""
    print("\n📋 Memory-Only Processing Best Practices")
    print("=" * 50)
    
    practices = [
        "🔒 Always use file_contents parameter (in-memory)",
        "💾 Never write result['model_patch'] directly to files",
        "🎯 Use file_locations for focused, efficient processing", 
        "📊 Monitor token usage with smaller context_window",
        "🛡️  Verify original files unchanged after processing",
        "🔄 Use result['file_changes'] for programmatic application",
        "⚡ Process multiple files in single LLM call for efficiency",
        "🧪 Test patches in isolated environment before applying"
    ]
    
    for practice in practices:
        print(f"  {practice}")
    
    print(f"\n🚀 Key Benefits:")
    print(f"  ✅ Zero risk to original repository")
    print(f"  ✅ No cleanup of temporary files needed")
    print(f"  ✅ Can process files that don't exist on disk")
    print(f"  ✅ Perfect for CI/CD and automated workflows")
    print(f"  ✅ Enables safe experimentation with fixes")


def main():
    """Run all examples"""
    print("🔒 MEMORY-ONLY USAGE GUIDE")
    print("=" * 70)
    
    print("This guide demonstrates safe, memory-only processing that:")
    print("  🔒 Never writes files to disk")
    print("  🛡️  Never modifies original repository")
    print("  💾 Returns all results in memory")
    print("  🚀 Provides full LLM processing capability")
    
    # Run examples
    try:
        # Example 1: Pure memory processing
        memory_only_example()
        
        # Example 2: Read from disk, process in memory
        read_files_from_disk_example()
        
        # Example 3: Manual patch application
        apply_patch_manually_example()
        
        # Best practices
        best_practices()
        
        print(f"\n🎉 All examples completed successfully!")
        print(f"🔒 Your repository remains completely untouched!")
        
    except Exception as e:
        print(f"❌ Error in examples: {e}")


if __name__ == "__main__":
    main()
