#!/usr/bin/env python3
"""
Chunk to Patch Generator

This module implements functionality to generate model patches from problematic code chunks.
It takes a problem statement and relevant code chunks as input, uses LLM to generate 
SEARCH/REPLACE format edits, and outputs a Git diff format patch.

Usage:
    python chunk_to_patch.py --problem "Issue description" --chunk "code chunk" --file "file.py"
"""

import argparse
import ast
import json
import os
import re
import subprocess
import tempfile
import uuid
from difflib import unified_diff
from typing import Dict, List, Tuple, Optional


class ChunkToPatchGenerator:
    """Generator that converts problematic code chunks to patches using LLM."""
    
    def __init__(self, model_name: str = "gpt-4", temperature: float = 0.0):
        """
        Initialize the generator.
        
        Args:
            model_name: Name of the LLM model to use
            temperature: Temperature for LLM generation
        """
        self.model_name = model_name
        self.temperature = temperature
        self.search_replace_prompt = self._get_search_replace_prompt()
    
    def _get_search_replace_prompt(self) -> str:
        """Get the prompt template for SEARCH/REPLACE format."""
        return """We are currently solving the following issue within our repository. Here is the issue text:
--- BEGIN ISSUE ---
{problem_statement}
--- END ISSUE ---

Here is the relevant code chunk that may contain the bug:
--- BEGIN CHUNK ---
File: {file_path}
```
{code_chunk}
```
--- END CHUNK ---

Please first localize the bug based on the issue statement, and then generate *SEARCH/REPLACE* edits to fix the issue.

Every *SEARCH/REPLACE* edit must use this format:
1. The file path
2. The start of search block: <<<<<<< SEARCH
3. A contiguous chunk of lines to search for in the existing source code
4. The dividing line: =======
5. The lines to replace into the source code
6. The end of the replace block: >>>>>>> REPLACE

Here is an example:

```python
### mathweb/flask/app.py
<<<<<<< SEARCH
from flask import Flask
=======
import math
from flask import Flask
>>>>>>> REPLACE
```

Please note that the *SEARCH/REPLACE* edit REQUIRES PROPER INDENTATION. If you would like to add the line '        print(x)', you must fully write that out, with all those spaces before the code!
Wrap the *SEARCH/REPLACE* edit in blocks ```python...```.
"""

    def extract_python_blocks(self, text: str) -> List[str]:
        """Extract Python code blocks from LLM output."""
        pattern = r"```python\n(.*?)\n```"
        matches = re.findall(pattern, text, re.DOTALL)
        return matches

    def parse_search_replace_commands(self, commands: List[str]) -> Dict[str, List[Dict]]:
        """
        Parse SEARCH/REPLACE commands and group by file.
        
        Args:
            commands: List of command strings from LLM output
            
        Returns:
            Dictionary mapping file paths to list of edit commands
        """
        file_to_commands = {}
        
        for command in commands:
            lines = command.strip().split('\n')
            current_file = None
            current_command = None
            search_lines = []
            replace_lines = []
            in_search = False
            in_replace = False
            
            for line in lines:
                # Check for file path
                if line.startswith('### '):
                    current_file = line[4:].strip()
                    continue
                
                # Check for search start
                if line.strip() == '<<<<<<< SEARCH':
                    in_search = True
                    in_replace = False
                    search_lines = []
                    continue
                
                # Check for divider
                if line.strip() == '=======':
                    in_search = False
                    in_replace = True
                    replace_lines = []
                    continue
                
                # Check for replace end
                if line.strip() == '>>>>>>> REPLACE':
                    in_search = False
                    in_replace = False
                    
                    if current_file and search_lines is not None:
                        search_text = '\n'.join(search_lines)
                        replace_text = '\n'.join(replace_lines)
                        
                        command_dict = {
                            'search': search_text,
                            'replace': replace_text
                        }
                        
                        if current_file not in file_to_commands:
                            file_to_commands[current_file] = []
                        file_to_commands[current_file].append(command_dict)
                    continue
                
                # Collect lines
                if in_search:
                    search_lines.append(line)
                elif in_replace:
                    replace_lines.append(line)
        
        return file_to_commands

    def apply_search_replace_edits(self, content: str, commands: List[Dict]) -> str:
        """
        Apply SEARCH/REPLACE edits to file content.
        
        Args:
            content: Original file content
            commands: List of search/replace command dictionaries
            
        Returns:
            Modified file content
        """
        modified_content = content
        
        # Apply edits in reverse order to maintain line numbers
        for command in reversed(commands):
            search_text = command['search']
            replace_text = command['replace']
            
            # Add newlines for proper matching
            search_pattern = '\n' + search_text + '\n'
            replace_pattern = '\n' + replace_text + '\n'
            
            # Try to find and replace
            if search_pattern in '\n' + modified_content + '\n':
                temp_content = '\n' + modified_content + '\n'
                temp_content = temp_content.replace(search_pattern, replace_pattern, 1)
                modified_content = temp_content[1:-1]  # Remove added newlines
            else:
                # Try without surrounding newlines
                if search_text in modified_content:
                    modified_content = modified_content.replace(search_text, replace_text, 1)
                else:
                    print(f"Warning: Could not find search text: {search_text[:50]}...")
        
        return modified_content

    def check_syntax(self, code: str) -> bool:
        """Check if the code has valid Python syntax."""
        try:
            ast.parse(code)
            return True
        except SyntaxError:
            return False

    def create_git_diff(self, file_path: str, original_content: str, new_content: str) -> str:
        """
        Create a Git diff format patch using a temporary Git repository.
        
        Args:
            file_path: Path to the file being modified
            original_content: Original file content
            new_content: Modified file content
            
        Returns:
            Git diff format string
        """
        with tempfile.TemporaryDirectory() as temp_dir:
            repo_dir = os.path.join(temp_dir, str(uuid.uuid4()))
            os.makedirs(repo_dir)
            
            # Initialize git repo
            subprocess.run(['git', 'init'], cwd=repo_dir, capture_output=True)
            subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], cwd=repo_dir)
            subprocess.run(['git', 'config', 'user.name', 'Test User'], cwd=repo_dir)
            
            # Create directory structure
            full_file_path = os.path.join(repo_dir, file_path)
            os.makedirs(os.path.dirname(full_file_path), exist_ok=True)
            
            # Write original file and commit
            with open(full_file_path, 'w') as f:
                f.write(original_content)
            
            subprocess.run(['git', 'add', file_path], cwd=repo_dir)
            subprocess.run(['git', 'commit', '-m', 'initial commit'], cwd=repo_dir)
            
            # Write modified file
            with open(full_file_path, 'w') as f:
                f.write(new_content)
            
            # Get git diff
            result = subprocess.run(['git', 'diff'], cwd=repo_dir, capture_output=True, text=True)
            return result.stdout

    def generate_patch(self, problem_statement: str, code_chunk: str, file_path: str, 
                      original_content: Optional[str] = None) -> Dict:
        """
        Generate a patch from a problematic code chunk.
        
        Args:
            problem_statement: Description of the issue to fix
            code_chunk: The problematic code chunk
            file_path: Path to the file containing the chunk
            original_content: Full original file content (if None, uses code_chunk)
            
        Returns:
            Dictionary containing patch information
        """
        # Use code_chunk as original_content if not provided
        if original_content is None:
            original_content = code_chunk
        
        # Format prompt
        message = self.search_replace_prompt.format(
            problem_statement=problem_statement,
            code_chunk=code_chunk,
            file_path=file_path
        )
        
        # For this implementation, we'll simulate LLM call
        # In real usage, you would call your LLM API here
        llm_output = self._simulate_llm_call(message)
        
        # Extract and parse commands
        python_blocks = self.extract_python_blocks(llm_output)
        if not python_blocks:
            return {
                "success": False,
                "error": "No Python blocks found in LLM output",
                "raw_output": llm_output
            }
        
        file_to_commands = self.parse_search_replace_commands(python_blocks)
        
        if file_path not in file_to_commands:
            return {
                "success": False,
                "error": f"No edits found for file {file_path}",
                "raw_output": llm_output
            }
        
        # Apply edits
        commands = file_to_commands[file_path]
        new_content = self.apply_search_replace_edits(original_content, commands)
        
        # Check syntax
        if not self.check_syntax(new_content):
            return {
                "success": False,
                "error": "Generated code has syntax errors",
                "raw_output": llm_output,
                "new_content": new_content
            }
        
        # Generate git diff
        git_diff = self.create_git_diff(file_path, original_content, new_content)
        
        return {
            "success": True,
            "model_patch": git_diff,
            "raw_output": llm_output,
            "original_content": original_content,
            "new_content": new_content,
            "edited_files": [file_path],
            "commands": commands
        }

    def _simulate_llm_call(self, message: str) -> str:
        """
        Simulate LLM call for demonstration purposes.
        In real usage, replace this with actual LLM API call.
        """
        # Extract file path from the message for more realistic simulation
        import re
        file_match = re.search(r'File: ([^\n]+)', message)
        file_path = file_match.group(1) if file_match else "example.py"

        # Extract code chunk for more realistic response
        chunk_match = re.search(r'```\n(.*?)\n```', message, re.DOTALL)
        if chunk_match:
            code_chunk = chunk_match.group(1)
            # Simple heuristic: if it returns None, replace with a string
            if 'return None' in code_chunk:
                fixed_code = code_chunk.replace('return None', 'return "Hello, World!"')
                return f"""Looking at the issue and code chunk, I can see the problem and will provide a fix.

```python
### {file_path}
<<<<<<< SEARCH
{code_chunk}
=======
{fixed_code}
>>>>>>> REPLACE
```
"""
            # If it's a math function, add abs() for negative numbers
            elif 'calculate_sum' in code_chunk and 'total += num' in code_chunk:
                fixed_code = code_chunk.replace('total += num', 'total += abs(num)  # Use absolute value')
                return f"""Looking at the issue and code chunk, I can see the problem and will provide a fix.

```python
### {file_path}
<<<<<<< SEARCH
{code_chunk}
=======
{fixed_code}
>>>>>>> REPLACE
```
"""

        # Default fallback
        return f"""Looking at the issue and code chunk, I can see the problem and will provide a fix.

```python
### {file_path}
<<<<<<< SEARCH
def buggy_function():
    return None
=======
def buggy_function():
    return "fixed"
>>>>>>> REPLACE
```
"""


def main():
    """Main function for command line usage."""
    parser = argparse.ArgumentParser(description='Generate patches from problematic code chunks')
    parser.add_argument('--problem', required=True, help='Problem statement describing the issue')
    parser.add_argument('--chunk', required=True, help='Problematic code chunk')
    parser.add_argument('--file', required=True, help='File path containing the chunk')
    parser.add_argument('--original', help='Full original file content (optional)')
    parser.add_argument('--model', default='gpt-4', help='LLM model to use')
    parser.add_argument('--output', help='Output file for the patch')

    args = parser.parse_args()

    # Initialize generator
    generator = ChunkToPatchGenerator(model_name=args.model)

    # Read original content if provided
    original_content = None
    if args.original:
        if os.path.exists(args.original):
            with open(args.original, 'r') as f:
                original_content = f.read()
        else:
            original_content = args.original

    # Generate patch
    result = generator.generate_patch(
        problem_statement=args.problem,
        code_chunk=args.chunk,
        file_path=args.file,
        original_content=original_content
    )

    # Output results
    if result['success']:
        print("Patch generated successfully!")
        print("\n" + "="*50)
        print("MODEL PATCH:")
        print("="*50)
        print(result['model_patch'])

        if args.output:
            with open(args.output, 'w') as f:
                f.write(result['model_patch'])
            print(f"\nPatch saved to: {args.output}")
    else:
        print(f"Error: {result['error']}")
        print(f"Raw output: {result.get('raw_output', 'N/A')}")

    # Print full result as JSON for debugging
    print("\n" + "="*50)
    print("FULL RESULT:")
    print("="*50)
    print(json.dumps(result, indent=2))


# Example usage and test functions
def example_usage():
    """Example of how to use the ChunkToPatchGenerator."""
    generator = ChunkToPatchGenerator()

    # Example problem and code chunk
    problem = """
    The function should return a greeting message but currently returns None.
    It should return "Hello, World!" instead.
    """

    code_chunk = """def greet():
    # TODO: implement greeting
    return None"""

    file_path = "greetings.py"

    result = generator.generate_patch(
        problem_statement=problem,
        code_chunk=code_chunk,
        file_path=file_path
    )

    print("Example Result:")
    print(json.dumps(result, indent=2))


def test_search_replace_parsing():
    """Test the SEARCH/REPLACE command parsing."""
    generator = ChunkToPatchGenerator()

    test_output = """```python
### test.py
<<<<<<< SEARCH
def old_function():
    return "old"
=======
def old_function():
    return "new"
>>>>>>> REPLACE
```"""

    blocks = generator.extract_python_blocks(test_output)
    commands = generator.parse_search_replace_commands(blocks)

    print("Test parsing result:")
    print(json.dumps(commands, indent=2))

    # Test applying edits
    original = """def old_function():
    return "old"

def other_function():
    pass"""

    new_content = generator.apply_search_replace_edits(original, commands['test.py'])
    print("\nOriginal content:")
    print(original)
    print("\nModified content:")
    print(new_content)


if __name__ == "__main__":
    main()
