#!/usr/bin/env python3
"""
LLM Client Module for Agentless Multi-file Patch Generator

This module provides a unified interface for calling various LLM APIs with proper
rate limiting, error handling, and retry mechanisms.
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime, timedelta
from collections import deque
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum

try:
    import nest_asyncio
    nest_asyncio.apply()
except ImportError:
    pass

# Import different LLM clients
try:
    from openai import AsyncOpenAI, OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False


class LLMProvider(Enum):
    """Supported LLM providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    DEEPSEEK = "deepseek"
    CUSTOM = "custom"


@dataclass
class LLMConfig:
    """Configuration for LLM client"""
    provider: LLMProvider
    model: str
    api_key: str
    base_url: Optional[str] = None
    max_tokens: int = 4096
    temperature: float = 0.0
    timeout: int = 120
    max_retries: int = 3
    retry_delay: float = 1.0
    max_requests_per_minute: int = 30


class RateLimiter:
    """Rate limiter to ensure requests don't exceed specified limits"""
    
    def __init__(self, max_requests_per_minute: int = 30):
        self.max_requests = max_requests_per_minute
        self.request_times = deque()
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire permission for a request, wait if rate limit exceeded"""
        async with self.lock:
            now = datetime.now()
            # Remove requests older than 1 minute
            while self.request_times and now - self.request_times[0] > timedelta(minutes=1):
                self.request_times.popleft()
            
            # If current minute requests reached limit, wait
            if len(self.request_times) >= self.max_requests:
                wait_until = self.request_times[0] + timedelta(minutes=1)
                wait_seconds = (wait_until - now).total_seconds()
                if wait_seconds > 0:
                    logging.info(f"Rate limit reached, waiting {wait_seconds:.1f} seconds...")
                    await asyncio.sleep(wait_seconds)
                    # Re-clean expired records
                    now = datetime.now()
                    while self.request_times and now - self.request_times[0] > timedelta(minutes=1):
                        self.request_times.popleft()
            
            # Record current request time
            self.request_times.append(now)


class LLMClient:
    """Unified LLM client supporting multiple providers"""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.rate_limiter = RateLimiter(config.max_requests_per_minute)
        self.logger = logging.getLogger(__name__)
        
        # Initialize the appropriate client
        self._init_client()
    
    def _init_client(self):
        """Initialize the appropriate LLM client based on provider"""
        if self.config.provider == LLMProvider.OPENAI:
            if not OPENAI_AVAILABLE:
                raise ImportError("OpenAI package not available. Install with: pip install openai")
            
            self.async_client = AsyncOpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url
            )
            self.sync_client = OpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url
            )
        
        elif self.config.provider == LLMProvider.DEEPSEEK:
            if not OPENAI_AVAILABLE:
                raise ImportError("OpenAI package not available. Install with: pip install openai")
            
            # DeepSeek uses OpenAI-compatible API
            base_url = self.config.base_url or "https://api.deepseek.com"
            self.async_client = AsyncOpenAI(
                api_key=self.config.api_key,
                base_url=base_url
            )
            self.sync_client = OpenAI(
                api_key=self.config.api_key,
                base_url=base_url
            )
        
        elif self.config.provider == LLMProvider.ANTHROPIC:
            if not ANTHROPIC_AVAILABLE:
                raise ImportError("Anthropic package not available. Install with: pip install anthropic")
            
            self.async_client = anthropic.AsyncAnthropic(api_key=self.config.api_key)
            self.sync_client = anthropic.Anthropic(api_key=self.config.api_key)
        
        elif self.config.provider == LLMProvider.CUSTOM:
            # For custom providers, assume OpenAI-compatible API
            if not OPENAI_AVAILABLE:
                raise ImportError("OpenAI package not available for custom provider")
            
            self.async_client = AsyncOpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url
            )
            self.sync_client = OpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url
            )
        
        else:
            raise ValueError(f"Unsupported provider: {self.config.provider}")
    
    async def _call_openai_async(self, message: str) -> Dict[str, Any]:
        """Call OpenAI/DeepSeek/Custom API asynchronously"""
        try:
            response = await self.async_client.chat.completions.create(
                model=self.config.model,
                messages=[{"role": "user", "content": message}],
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                timeout=self.config.timeout
            )
            
            return {
                "success": True,
                "response": response.choices[0].message.content,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "model": response.model
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    def _call_openai_sync(self, message: str) -> Dict[str, Any]:
        """Call OpenAI/DeepSeek/Custom API synchronously"""
        try:
            response = self.sync_client.chat.completions.create(
                model=self.config.model,
                messages=[{"role": "user", "content": message}],
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                timeout=self.config.timeout
            )
            
            return {
                "success": True,
                "response": response.choices[0].message.content,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "model": response.model
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    async def _call_anthropic_async(self, message: str) -> Dict[str, Any]:
        """Call Anthropic API asynchronously"""
        try:
            response = await self.async_client.messages.create(
                model=self.config.model,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                messages=[{"role": "user", "content": message}]
            )
            
            return {
                "success": True,
                "response": response.content[0].text,
                "usage": {
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                },
                "model": response.model
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    def _call_anthropic_sync(self, message: str) -> Dict[str, Any]:
        """Call Anthropic API synchronously"""
        try:
            response = self.sync_client.messages.create(
                model=self.config.model,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                messages=[{"role": "user", "content": message}]
            )
            
            return {
                "success": True,
                "response": response.content[0].text,
                "usage": {
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens
                },
                "model": response.model
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    async def call_async(self, message: str) -> Dict[str, Any]:
        """Call LLM asynchronously with rate limiting and retries"""
        await self.rate_limiter.acquire()
        
        for attempt in range(self.config.max_retries):
            try:
                if self.config.provider in [LLMProvider.OPENAI, LLMProvider.DEEPSEEK, LLMProvider.CUSTOM]:
                    result = await self._call_openai_async(message)
                elif self.config.provider == LLMProvider.ANTHROPIC:
                    result = await self._call_anthropic_async(message)
                else:
                    raise ValueError(f"Unsupported provider: {self.config.provider}")
                
                if result["success"]:
                    return result
                else:
                    self.logger.warning(f"Attempt {attempt + 1} failed: {result['error']}")
                    if attempt < self.config.max_retries - 1:
                        await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
            
            except Exception as e:
                self.logger.error(f"Attempt {attempt + 1} error: {e}")
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
        
        return {
            "success": False,
            "error": f"Failed after {self.config.max_retries} attempts",
            "error_type": "MaxRetriesExceeded"
        }
    
    def call_sync(self, message: str) -> Dict[str, Any]:
        """Call LLM synchronously with retries"""
        for attempt in range(self.config.max_retries):
            try:
                if self.config.provider in [LLMProvider.OPENAI, LLMProvider.DEEPSEEK, LLMProvider.CUSTOM]:
                    result = self._call_openai_sync(message)
                elif self.config.provider == LLMProvider.ANTHROPIC:
                    result = self._call_anthropic_sync(message)
                else:
                    raise ValueError(f"Unsupported provider: {self.config.provider}")
                
                if result["success"]:
                    return result
                else:
                    self.logger.warning(f"Attempt {attempt + 1} failed: {result['error']}")
                    if attempt < self.config.max_retries - 1:
                        time.sleep(self.config.retry_delay * (2 ** attempt))
            
            except Exception as e:
                self.logger.error(f"Attempt {attempt + 1} error: {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay * (2 ** attempt))
        
        return {
            "success": False,
            "error": f"Failed after {self.config.max_retries} attempts",
            "error_type": "MaxRetriesExceeded"
        }
    
    async def close(self):
        """Close the async client"""
        if hasattr(self.async_client, 'close'):
            await self.async_client.close()


def create_llm_client(provider: str, model: str, api_key: str, 
                     base_url: Optional[str] = None, **kwargs) -> LLMClient:
    """
    Factory function to create LLM client
    
    Args:
        provider: LLM provider ("openai", "anthropic", "deepseek", "custom")
        model: Model name
        api_key: API key
        base_url: Base URL for API (optional)
        **kwargs: Additional configuration options
    
    Returns:
        LLMClient instance
    """
    provider_enum = LLMProvider(provider.lower())
    
    config = LLMConfig(
        provider=provider_enum,
        model=model,
        api_key=api_key,
        base_url=base_url,
        **kwargs
    )
    
    return LLMClient(config)


# Convenience functions for common providers
def create_openai_client(model: str = "gpt-4", api_key: Optional[str] = None, **kwargs) -> LLMClient:
    """Create OpenAI client"""
    api_key = api_key or os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OpenAI API key not provided")
    
    return create_llm_client("openai", model, api_key, **kwargs)


def create_anthropic_client(model: str = "claude-3-sonnet-20240229", api_key: Optional[str] = None, **kwargs) -> LLMClient:
    """Create Anthropic client"""
    api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
    if not api_key:
        raise ValueError("Anthropic API key not provided")
    
    return create_llm_client("anthropic", model, api_key, **kwargs)


def create_deepseek_client(model: str = "deepseek-coder", api_key: Optional[str] = None, **kwargs) -> LLMClient:
    """Create DeepSeek client"""
    api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        raise ValueError("DeepSeek API key not provided")
    
    return create_llm_client("deepseek", model, api_key, "https://api.deepseek.com", **kwargs)
