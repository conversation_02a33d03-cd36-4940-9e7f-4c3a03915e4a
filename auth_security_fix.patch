diff --git a/auth.py b/auth.py
index d64427e..a2fca55 100644
--- a/auth.py
+++ b/auth.py
@@ -1,10 +1,22 @@
+import hashlib
+import secrets
+
 def hash_password(password):
-    # Bug: no actual hashing
-    return password
+    # Generate a random salt
+    salt = secrets.token_hex(16)
+    # Hash the password with salt using SHA-256
+    password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
+    return f"{salt}:{password_hash}"
 
 def verify_password(password, stored):
-    # Bug: plain text comparison
-    return password == stored
+    # Extract salt and hash from stored password
+    try:
+        salt, stored_hash = stored.split(':')
+        # Hash the provided password with the same salt
+        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
+        return password_hash == stored_hash
+    except ValueError:
+        return False
 
 def authenticate(username, password):
     user = get_user(username)
diff --git a/main.py b/main.py
index 0e1110f..e04d250 100644
--- a/main.py
+++ b/main.py
@@ -2,16 +2,34 @@ from auth import authenticate
 from user import create_user
 
 def login(username, password):
-    # Bug: no error handling
-    if authenticate(username, password):
-        print("Login successful")
-        return True
-    else:
-        print("Login failed")
+    try:
+        # Input sanitization
+        if not username or not password:
+            print("Error: Username and password are required")
+            return False
+        
+        if authenticate(username.strip(), password):
+            print("Login successful")
+            return True
+        else:
+            print("Login failed: Invalid username or password")
+            return False
+    except Exception as e:
+        print(f"Login error: An unexpected error occurred")
+        # Log the actual error for debugging (don't expose to user)
+        # logger.error(f"Login error for user {username}: {str(e)}")
         return False
 
 def register(username, password, email):
-    # Bug: no error handling
-    user = create_user(username, password, email)
-    print(f"User {username} created")
-    return user
\ No newline at end of file
+    try:
+        user = create_user(username, password, email)
+        print(f"User {username} created successfully")
+        return user
+    except ValueError as e:
+        print(f"Registration failed: {str(e)}")
+        return None
+    except Exception as e:
+        print("Registration failed: An unexpected error occurred")
+        # Log the actual error for debugging (don't expose to user)
+        # logger.error(f"Registration error: {str(e)}")
+        return None
\ No newline at end of file
diff --git a/user.py b/user.py
index ddbbacf..de4084b 100644
--- a/user.py
+++ b/user.py
@@ -1,11 +1,42 @@
+import re
 from auth import hash_password
 
+def validate_input(username, password, email):
+    """Validate user input for registration"""
+    errors = []
+    
+    # Username validation
+    if not username or len(username.strip()) < 3:
+        errors.append("Username must be at least 3 characters long")
+    if not re.match("^[a-zA-Z0-9_]+$", username):
+        errors.append("Username can only contain letters, numbers, and underscores")
+    
+    # Password validation
+    if not password or len(password) < 8:
+        errors.append("Password must be at least 8 characters long")
+    if not re.search(r"[A-Z]", password):
+        errors.append("Password must contain at least one uppercase letter")
+    if not re.search(r"[a-z]", password):
+        errors.append("Password must contain at least one lowercase letter")
+    if not re.search(r"\d", password):
+        errors.append("Password must contain at least one digit")
+    
+    # Email validation
+    if not email or not re.match(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", email):
+        errors.append("Please provide a valid email address")
+    
+    return errors
+
 def create_user(username, password, email):
-    # Bug: no input validation
+    # Input validation
+    validation_errors = validate_input(username, password, email)
+    if validation_errors:
+        raise ValueError("Validation failed: " + "; ".join(validation_errors))
+    
     user = {
-        'username': username,
+        'username': username.strip(),
         'password': hash_password(password),
-        'email': email
+        'email': email.strip().lower()
     }
     save_user(user)
     return user
