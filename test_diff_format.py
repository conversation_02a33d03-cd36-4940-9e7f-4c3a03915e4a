#!/usr/bin/env python3
"""
Test script to verify the Git diff format generation.
"""

from agentless_multifile_patch import AgentlessMultifilePatchGenerator

def test_diff_format():
    """Test the Git diff format generation."""
    
    # Create generator without LLM (will use simulation)
    generator = AgentlessMultifilePatchGenerator()
    
    # Test case: astropy separable.py fix
    original_content = """def _cstack(left, right):
    \"\"\"
    Function corresponding to the stack operation.
    \"\"\"
    cright = _coord_matrix(right, 'right', noutp)
    else:
        cright = np.zeros((noutp, right.shape[1]))
        cright[-right.shape[0]:, -right.shape[1]:] = 1

    return np.hstack([cleft, cright])
"""

    new_content = """def _cstack(left, right):
    \"\"\"
    Function corresponding to the stack operation.
    \"\"\"
    cright = _coord_matrix(right, 'right', noutp)
    else:
        cright = np.zeros((noutp, right.shape[1]))
        cright[-right.shape[0]:, -right.shape[1]:] = right

    return np.hstack([cleft, cright])
"""

    # Test file changes
    file_changes = {
        "separable.py": (original_content, new_content)
    }
    
    # Generate diff with module prefix
    diff = generator.create_git_diff(file_changes, module_prefix="astropy/modeling")
    
    print("Generated Git diff:")
    print("=" * 60)
    print(diff)
    print("=" * 60)
    
    # Check if the format matches expected
    expected_patterns = [
        "diff --git a/astropy/modeling/separable.py b/astropy/modeling/separable.py",
        "index ",
        "--- a/astropy/modeling/separable.py",
        "+++ b/astropy/modeling/separable.py",
        "-        cright[-right.shape[0]:, -right.shape[1]:] = 1",
        "+        cright[-right.shape[0]:, -right.shape[1]:] = right"
    ]
    
    success = True
    for pattern in expected_patterns:
        if pattern not in diff:
            print(f"❌ Missing pattern: {pattern}")
            success = False
        else:
            print(f"✅ Found pattern: {pattern}")
    
    if success:
        print("\n🎉 Git diff format is correct!")
    else:
        print("\n❌ Git diff format has issues!")
    
    return success

if __name__ == "__main__":
    test_diff_format()
