#!/usr/bin/env python3
"""
LLM Configuration Examples

This file shows different ways to configure the AgentlessMultifilePatchGenerator
with various LLM providers and settings.
"""

import os
from agentless_multifile_patch import AgentlessMultifilePatchGenerator


def create_openai_generator():
    """Create generator configured for OpenAI"""
    return AgentlessMultifilePatchGenerator(
        model_name="gpt-4",
        llm_provider="openai",
        api_key=os.getenv("OPENAI_API_KEY"),
        temperature=0.0,
        max_tokens=4096,
        max_context_length=8000
    )


def create_openai_turbo_generator():
    """Create generator configured for OpenAI GPT-4 Turbo"""
    return AgentlessMultifilePatchGenerator(
        model_name="gpt-4-turbo",
        llm_provider="openai",
        api_key=os.getenv("OPENAI_API_KEY"),
        temperature=0.1,  # Slightly higher for more creativity
        max_tokens=4096,
        max_context_length=16000  # Higher context for turbo
    )


def create_anthropic_generator():
    """Create generator configured for Anthropic Claude"""
    return AgentlessMultifilePatchGenerator(
        model_name="claude-3-sonnet-20240229",
        llm_provider="anthropic",
        api_key=os.getenv("ANTHROPIC_API_KEY"),
        temperature=0.0,
        max_tokens=4096,
        max_context_length=12000
    )


def create_anthropic_opus_generator():
    """Create generator configured for Anthropic Claude Opus"""
    return AgentlessMultifilePatchGenerator(
        model_name="claude-3-opus-20240229",
        llm_provider="anthropic",
        api_key=os.getenv("ANTHROPIC_API_KEY"),
        temperature=0.0,
        max_tokens=4096,
        max_context_length=15000,
        # Additional Anthropic-specific settings
        max_requests_per_minute=20  # Lower rate limit for Opus
    )


def create_deepseek_generator():
    """Create generator configured for DeepSeek"""
    return AgentlessMultifilePatchGenerator(
        model_name="deepseek-coder",
        llm_provider="deepseek",
        api_key=os.getenv("DEEPSEEK_API_KEY"),
        temperature=0.0,
        max_tokens=4096,
        max_context_length=8000
    )


def create_custom_generator():
    """Create generator configured for custom OpenAI-compatible API"""
    return AgentlessMultifilePatchGenerator(
        model_name="custom-model-name",
        llm_provider="custom",
        api_key=os.getenv("CUSTOM_API_KEY"),
        base_url="https://your-custom-api.com/v1",  # Custom API endpoint
        temperature=0.0,
        max_tokens=4096,
        max_context_length=8000,
        # Custom settings
        max_requests_per_minute=60,
        max_retries=5,
        timeout=180
    )


def create_local_llm_generator():
    """Create generator configured for local LLM (e.g., Ollama)"""
    return AgentlessMultifilePatchGenerator(
        model_name="codellama:13b",
        llm_provider="custom",
        api_key="not-needed",  # Local APIs often don't need keys
        base_url="http://localhost:11434/v1",  # Ollama default endpoint
        temperature=0.1,
        max_tokens=2048,  # Local models often have lower limits
        max_context_length=4000,
        max_requests_per_minute=120,  # Higher rate for local
        timeout=300  # Longer timeout for local processing
    )


# Configuration presets
PROVIDER_CONFIGS = {
    "openai_gpt4": {
        "model_name": "gpt-4",
        "llm_provider": "openai",
        "temperature": 0.0,
        "max_tokens": 4096,
        "max_context_length": 8000
    },
    
    "openai_gpt4_turbo": {
        "model_name": "gpt-4-turbo",
        "llm_provider": "openai",
        "temperature": 0.0,
        "max_tokens": 4096,
        "max_context_length": 16000
    },
    
    "anthropic_sonnet": {
        "model_name": "claude-3-sonnet-20240229",
        "llm_provider": "anthropic",
        "temperature": 0.0,
        "max_tokens": 4096,
        "max_context_length": 12000
    },
    
    "anthropic_opus": {
        "model_name": "claude-3-opus-20240229",
        "llm_provider": "anthropic",
        "temperature": 0.0,
        "max_tokens": 4096,
        "max_context_length": 15000,
        "max_requests_per_minute": 20
    },
    
    "deepseek_coder": {
        "model_name": "deepseek-coder",
        "llm_provider": "deepseek",
        "temperature": 0.0,
        "max_tokens": 4096,
        "max_context_length": 8000
    }
}


def create_generator_from_config(config_name: str, api_key: str = None, **overrides):
    """
    Create generator from predefined configuration
    
    Args:
        config_name: Name of the configuration preset
        api_key: API key (if not provided, will try environment variables)
        **overrides: Override any configuration parameters
    
    Returns:
        AgentlessMultifilePatchGenerator instance
    """
    if config_name not in PROVIDER_CONFIGS:
        raise ValueError(f"Unknown config: {config_name}. Available: {list(PROVIDER_CONFIGS.keys())}")
    
    config = PROVIDER_CONFIGS[config_name].copy()
    config.update(overrides)
    
    # Set API key based on provider
    if not api_key:
        provider = config["llm_provider"]
        if provider == "openai":
            api_key = os.getenv("OPENAI_API_KEY")
        elif provider == "anthropic":
            api_key = os.getenv("ANTHROPIC_API_KEY")
        elif provider == "deepseek":
            api_key = os.getenv("DEEPSEEK_API_KEY")
    
    config["api_key"] = api_key
    
    return AgentlessMultifilePatchGenerator(**config)


def test_configuration(config_name: str):
    """Test a configuration with a simple example"""
    print(f"Testing configuration: {config_name}")
    print("-" * 50)
    
    try:
        generator = create_generator_from_config(config_name)
        
        # Simple test
        problem = "Fix the function to return 'Hello, World!' instead of None"
        file_contents = {
            "test.py": """def greet():
    return None  # Bug: should return greeting"""
        }
        
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            diff_format=True
        )
        
        if result['success']:
            print("✓ Configuration working correctly")
            print(f"Generated patch for {len(result['edited_files'])} files")
        else:
            print(f"✗ Configuration failed: {result['error']}")
            
    except Exception as e:
        print(f"✗ Configuration error: {e}")
    
    print()


def main():
    """Test different configurations"""
    print("LLM Configuration Examples")
    print("=" * 50)
    
    print("Available configurations:")
    for name, config in PROVIDER_CONFIGS.items():
        print(f"  {name}: {config['llm_provider']} - {config['model_name']}")
    
    print("\nTesting configurations with available API keys...")
    print()
    
    # Test configurations based on available API keys
    if os.getenv("OPENAI_API_KEY"):
        test_configuration("openai_gpt4")
        test_configuration("openai_gpt4_turbo")
    
    if os.getenv("ANTHROPIC_API_KEY"):
        test_configuration("anthropic_sonnet")
        test_configuration("anthropic_opus")
    
    if os.getenv("DEEPSEEK_API_KEY"):
        test_configuration("deepseek_coder")
    
    print("Configuration testing completed!")


if __name__ == "__main__":
    main()
