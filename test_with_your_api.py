#!/usr/bin/env python3
"""
Test script using your specific API configuration

This script tests the AgentlessMultifilePatchGenerator with your API settings.
"""

import os
import json
from agentless_multifile_patch import AgentlessMultifilePatchGenerator


def test_with_your_api():
    """Test using your specific API configuration"""
    print("="*70)
    print("TEST: Using Your API Configuration")
    print("="*70)
    
    # Your API configuration
    api_key = "1830498799983480864"
    base_url = "https://aigc.sankuai.com/v1/openai/native"
    
    # Initialize generator with your API settings
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",  # Based on your previous usage
        llm_provider="custom",  # Use custom provider for non-standard APIs
        api_key=api_key,
        base_url=base_url,
        temperature=0.0,
        max_tokens=4096,
        max_requests_per_minute=50,
        max_retries=3,
        timeout=120
    )
    
    problem = """
    There's a critical bug in the calculator module where division by zero is not handled properly.
    The main.py file calls the calculator functions but doesn't handle the exceptions that can be raised.
    Both files need to be fixed:
    1. calculator.py should raise appropriate exceptions for invalid operations
    2. main.py should handle these exceptions gracefully
    """
    
    file_contents = {
        "calculator.py": """def add(a, b):
    return a + b

def subtract(a, b):
    return a - b

def multiply(a, b):
    return a * b

def divide(a, b):
    return a / b  # Bug: no zero check

def power(a, b):
    return a ** b""",
        
        "main.py": """from calculator import add, subtract, multiply, divide, power

def main():
    a, b = 10, 0
    
    print("Calculator Results:")
    print(f"Addition: {add(a, b)}")
    print(f"Subtraction: {subtract(a, b)}")
    print(f"Multiplication: {multiply(a, b)}")
    print(f"Division: {divide(a, b)}")  # Bug: will crash here
    print(f"Power: {power(a, b)}")

if __name__ == "__main__":
    main()"""
    }
    
    print("Problem:", problem.strip())
    print(f"\nFiles to process: {list(file_contents.keys())}")
    print(f"\nUsing API: {base_url}")
    print(f"Model: anthropic.claude-sonnet-4")
    print("\nCalling LLM API...")
    
    try:
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            diff_format=True  # Use SEARCH/REPLACE format
        )
        
        print(f"\nResult success: {result['success']}")
        
        if result['success']:
            print(f"Edited files: {result['edited_files']}")
            print("\nGenerated multi-file patch:")
            print(result['model_patch'])
            
            # Save patch to file
            with open("your_api_calculator_fix.patch", "w") as f:
                f.write(result['model_patch'])
            print("\nPatch saved to: your_api_calculator_fix.patch")
            
            # Save full result for analysis
            with open("your_api_full_result.json", "w") as f:
                # Remove large content for JSON serialization
                json_result = result.copy()
                if 'file_changes' in json_result:
                    json_result['file_changes'] = {
                        k: f"({len(v[0])} -> {len(v[1])} chars)" 
                        for k, v in json_result['file_changes'].items()
                    }
                json.dump(json_result, f, indent=2)
            print("Full result saved to: your_api_full_result.json")
            
        else:
            print(f"Error: {result['error']}")
            if 'raw_output' in result:
                print(f"Raw LLM output: {result['raw_output'][:500]}...")
        
        return result['success']
        
    except Exception as e:
        print(f"Exception during API test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_simple_case_with_your_api():
    """Test a simple case with your API"""
    print("\n" + "="*70)
    print("TEST: Simple Case with Your API")
    print("="*70)
    
    # Your API configuration
    api_key = "1830498799983480864"
    base_url = "https://aigc.sankuai.com/v1/openai/native"
    
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",
        llm_provider="custom",
        api_key=api_key,
        base_url=base_url,
        temperature=0.0,
        max_tokens=2048
    )
    
    problem = "Fix the function to return a greeting message instead of None"
    file_contents = {
        "greetings.py": """def greet():
    return None  # Bug: should return greeting"""
    }
    
    print("Problem:", problem)
    print("File content:", file_contents["greetings.py"])
    print("\nCalling LLM API...")
    
    try:
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            diff_format=True
        )
        
        print(f"\nResult success: {result['success']}")
        
        if result['success']:
            print(f"Edited files: {result['edited_files']}")
            print("\nGenerated patch:")
            print(result['model_patch'])
        else:
            print(f"Error: {result['error']}")
        
        return result['success']
        
    except Exception as e:
        print(f"Exception: {e}")
        return False


def test_authentication_scenario():
    """Test a more complex authentication scenario"""
    print("\n" + "="*70)
    print("TEST: Authentication Security Fix")
    print("="*70)
    
    # Your API configuration
    api_key = "1830498799983480864"
    base_url = "https://aigc.sankuai.com/v1/openai/native"
    
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",
        llm_provider="custom",
        api_key=api_key,
        base_url=base_url,
        temperature=0.0,
        max_tokens=4096
    )
    
    problem = """
    The authentication system has security vulnerabilities:
    1. Passwords are stored in plain text in auth.py
    2. No input validation for user registration in user.py
    3. Authentication errors are not handled properly in main.py
    Fix these security issues across all related files.
    """
    
    file_contents = {
        "auth.py": """def hash_password(password):
    # Bug: no actual hashing
    return password

def verify_password(password, stored):
    # Bug: plain text comparison
    return password == stored

def authenticate(username, password):
    user = get_user(username)
    if user and verify_password(password, user['password']):
        return True
    return False""",
        
        "user.py": """from auth import hash_password

def create_user(username, password, email):
    # Bug: no input validation
    user = {
        'username': username,
        'password': hash_password(password),
        'email': email
    }
    save_user(user)
    return user

def get_user(username):
    # Mock implementation
    return {'username': username, 'password': 'plain_password'}""",
        
        "main.py": """from auth import authenticate
from user import create_user

def login(username, password):
    # Bug: no error handling
    if authenticate(username, password):
        print("Login successful")
        return True
    else:
        print("Login failed")
        return False

def register(username, password, email):
    # Bug: no error handling
    user = create_user(username, password, email)
    print(f"User {username} created")
    return user"""
    }
    
    print("Problem:", problem.strip())
    print(f"\nFiles to process: {list(file_contents.keys())}")
    print("\nCalling LLM API for complex multi-file fix...")
    
    try:
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            diff_format=True
        )
        
        print(f"\nResult success: {result['success']}")
        
        if result['success']:
            print(f"Edited files: {result['edited_files']}")
            print("\nGenerated multi-file patch:")
            print(result['model_patch'])
            
            # Save patch
            with open("auth_security_fix.patch", "w") as f:
                f.write(result['model_patch'])
            print("\nPatch saved to: auth_security_fix.patch")
        else:
            print(f"Error: {result['error']}")
        
        return result['success']
        
    except Exception as e:
        print(f"Exception: {e}")
        return False


def main():
    """Run tests with your API configuration"""
    print("Testing Agentless Multi-file Patch Generator with Your API")
    print("=" * 70)
    
    print("API Configuration:")
    print("  Base URL: https://aigc.sankuai.com/v1/openai/native")
    print("  Model: anthropic.claude-sonnet-4")
    print("  Rate Limit: 50 requests/minute")
    
    # Run tests
    results = []
    
    print("\n" + "="*70)
    print("RUNNING TESTS")
    print("="*70)
    
    # Test 1: Simple case
    results.append(("Simple Case", test_simple_case_with_your_api()))
    
    # Test 2: Calculator bug fix
    results.append(("Calculator Bug Fix", test_with_your_api()))
    
    # Test 3: Authentication security
    results.append(("Authentication Security", test_authentication_scenario()))
    
    # Summary
    print("\n" + "="*70)
    print("TEST SUMMARY")
    print("="*70)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your API integration is working correctly.")
    else:
        print("❌ Some tests failed. Check the error messages above.")
    
    print("\nGenerated files:")
    print("  - your_api_calculator_fix.patch")
    print("  - your_api_full_result.json")
    print("  - auth_security_fix.patch")


if __name__ == "__main__":
    main()
