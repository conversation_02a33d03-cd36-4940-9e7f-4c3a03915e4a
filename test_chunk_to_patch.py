#!/usr/bin/env python3
"""
Test script for chunk_to_patch.py

This script demonstrates how to use the ChunkToPatchGenerator and provides
various test cases to validate the functionality.
"""

import json
from chunk_to_patch import ChunkToPatchGenerator


def test_basic_functionality():
    """Test basic functionality with a simple example."""
    print("="*60)
    print("TEST 1: Basic Functionality")
    print("="*60)
    
    generator = ChunkToPatchGenerator()
    
    problem = """
    The function should return a greeting message but currently returns None.
    It should return "Hello, World!" instead of None.
    """
    
    code_chunk = """def greet():
    # TODO: implement greeting
    return None"""
    
    file_path = "greetings.py"
    
    result = generator.generate_patch(
        problem_statement=problem,
        code_chunk=code_chunk,
        file_path=file_path
    )
    
    print("Problem:", problem.strip())
    print("\nCode Chunk:")
    print(code_chunk)
    print("\nResult:")
    print(json.dumps(result, indent=2))


def test_with_original_content():
    """Test with full original file content."""
    print("\n" + "="*60)
    print("TEST 2: With Original File Content")
    print("="*60)
    
    generator = ChunkToPatchGenerator()
    
    problem = """
    The calculate_sum function has a bug where it doesn't handle negative numbers correctly.
    It should return the absolute sum of all numbers.
    """
    
    code_chunk = """def calculate_sum(numbers):
    total = 0
    for num in numbers:
        total += num
    return total"""
    
    original_content = """# Math utilities module

def calculate_sum(numbers):
    total = 0
    for num in numbers:
        total += num
    return total

def calculate_average(numbers):
    if not numbers:
        return 0
    return calculate_sum(numbers) / len(numbers)

def main():
    test_numbers = [1, -2, 3, -4, 5]
    print(f"Sum: {calculate_sum(test_numbers)}")
    print(f"Average: {calculate_average(test_numbers)}")

if __name__ == "__main__":
    main()"""
    
    file_path = "math_utils.py"
    
    result = generator.generate_patch(
        problem_statement=problem,
        code_chunk=code_chunk,
        file_path=file_path,
        original_content=original_content
    )
    
    print("Problem:", problem.strip())
    print("\nCode Chunk:")
    print(code_chunk)
    print("\nOriginal Content Length:", len(original_content))
    print("\nResult:")
    print(json.dumps(result, indent=2))


def test_search_replace_parsing():
    """Test the SEARCH/REPLACE command parsing functionality."""
    print("\n" + "="*60)
    print("TEST 3: SEARCH/REPLACE Parsing")
    print("="*60)
    
    generator = ChunkToPatchGenerator()
    
    # Test LLM output with SEARCH/REPLACE format
    test_llm_output = """Looking at the code, I can see the issue. Here's the fix:

```python
### math_utils.py
<<<<<<< SEARCH
def calculate_sum(numbers):
    total = 0
    for num in numbers:
        total += num
    return total
=======
def calculate_sum(numbers):
    total = 0
    for num in numbers:
        total += abs(num)  # Use absolute value
    return total
>>>>>>> REPLACE
```

This change will ensure that negative numbers are treated as positive when calculating the sum."""
    
    # Test extraction
    blocks = generator.extract_python_blocks(test_llm_output)
    print("Extracted Python blocks:")
    for i, block in enumerate(blocks):
        print(f"Block {i+1}:")
        print(block)
        print("-" * 40)
    
    # Test parsing
    commands = generator.parse_search_replace_commands(blocks)
    print("\nParsed commands:")
    print(json.dumps(commands, indent=2))
    
    # Test applying edits
    original = """def calculate_sum(numbers):
    total = 0
    for num in numbers:
        total += num
    return total

def other_function():
    pass"""
    
    if 'math_utils.py' in commands:
        new_content = generator.apply_search_replace_edits(original, commands['math_utils.py'])
        print("\nOriginal content:")
        print(original)
        print("\nModified content:")
        print(new_content)
        
        # Test syntax check
        syntax_ok = generator.check_syntax(new_content)
        print(f"\nSyntax check passed: {syntax_ok}")


def test_multiple_edits():
    """Test handling multiple SEARCH/REPLACE edits in one file."""
    print("\n" + "="*60)
    print("TEST 4: Multiple Edits")
    print("="*60)
    
    generator = ChunkToPatchGenerator()
    
    test_llm_output = """I need to make several changes to fix the issues:

```python
### calculator.py
<<<<<<< SEARCH
def add(a, b):
    return a + b
=======
def add(a, b):
    # Add two numbers
    return a + b
>>>>>>> REPLACE
```

```python
### calculator.py
<<<<<<< SEARCH
def divide(a, b):
    return a / b
=======
def divide(a, b):
    # Divide two numbers with zero check
    if b == 0:
        raise ValueError("Cannot divide by zero")
    return a / b
>>>>>>> REPLACE
```"""
    
    blocks = generator.extract_python_blocks(test_llm_output)
    commands = generator.parse_search_replace_commands(blocks)
    
    print("Parsed commands:")
    print(json.dumps(commands, indent=2))
    
    original = """def add(a, b):
    return a + b

def subtract(a, b):
    return a - b

def divide(a, b):
    return a / b

def multiply(a, b):
    return a * b"""
    
    if 'calculator.py' in commands:
        new_content = generator.apply_search_replace_edits(original, commands['calculator.py'])
        print("\nOriginal content:")
        print(original)
        print("\nModified content:")
        print(new_content)


def test_git_diff_generation():
    """Test Git diff generation."""
    print("\n" + "="*60)
    print("TEST 5: Git Diff Generation")
    print("="*60)
    
    generator = ChunkToPatchGenerator()
    
    original_content = """def hello():
    return "Hello"

def world():
    return "World"
"""
    
    new_content = """def hello():
    return "Hello"

def world():
    return "World!"

def greeting():
    return hello() + ", " + world()
"""
    
    git_diff = generator.create_git_diff("example.py", original_content, new_content)
    
    print("Original content:")
    print(original_content)
    print("New content:")
    print(new_content)
    print("Git diff:")
    print(git_diff)


def run_all_tests():
    """Run all test functions."""
    print("Running all tests for ChunkToPatchGenerator...")
    
    test_basic_functionality()
    test_with_original_content()
    test_search_replace_parsing()
    test_multiple_edits()
    test_git_diff_generation()
    
    print("\n" + "="*60)
    print("ALL TESTS COMPLETED")
    print("="*60)


if __name__ == "__main__":
    run_all_tests()
