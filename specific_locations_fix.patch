diff --git a/calculator.py b/calculator.py
index 0f262d8..cf470d9 100644
--- a/calculator.py
+++ b/calculator.py
@@ -5,7 +5,9 @@ def add(a, b):
     return a + b
 
 def divide(a, b):
-    return a / b  # Line 8: Bug - no zero check
+    if b == 0:
+        raise ValueError("Cannot divide by zero")
+    return a / b
 
 def multiply(a, b):
     return a * b
diff --git a/main.py b/main.py
index 37f1fed..480d908 100644
--- a/main.py
+++ b/main.py
@@ -12,8 +12,11 @@ def calculate_result(operation, a, b):
 
 def main():
     a, b = 10, 0
-    result = divide(a, b)  # Line 15: Bug - no exception handling
-    print(f"Result: {result}")
+    try:
+        result = divide(a, b)
+        print(f"Result: {result}")
+    except ValueError as e:
+        print(f"Error: {e}")
 
 if __name__ == "__main__":
     main()
\ No newline at end of file
diff --git a/validator.py b/validator.py
index c98ceca..4b26bcf 100644
--- a/validator.py
+++ b/validator.py
@@ -4,7 +4,7 @@ def validate_number(value):
     return isinstance(value, (int, float))
 
 def validate_operation(op):
-    return op in ["add", "subtract"]  # Line 6: Bug - incomplete list
+    return op in ["add", "subtract", "multiply", "divide"]
 
 def validate_input(operation, a, b):
     if not validate_operation(operation):
