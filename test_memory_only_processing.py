#!/usr/bin/env python3
"""
Test Memory-Only Processing

This script demonstrates that the AgentlessMultifilePatchGenerator works entirely
in memory without saving any files to disk or modifying the original repository.

🔒 MEMORY-ONLY GUARANTEE:
- No files are written to disk
- No temporary files are created
- Original repository remains untouched
- All processing happens in memory
"""

import os
import tempfile
import shutil
from agentless_multifile_patch import AgentlessMultifilePatchGenerator


def test_memory_only_processing():
    """Test that everything works in memory without touching filesystem"""
    print("="*70)
    print("🔒 MEMORY-ONLY PROCESSING TEST")
    print("="*70)
    
    # Your API configuration
    api_key = "1830498799983480864"
    base_url = "https://aigc.sankuai.com/v1/openai/native"
    
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",
        llm_provider="custom",
        api_key=api_key,
        base_url=base_url,
        temperature=0.0,
        max_tokens=2048
    )
    
    # Create file contents in memory (no files on disk!)
    problem = "Fix the security vulnerabilities in the authentication system"
    
    file_contents = {
        "auth.py": """import hashlib

def hash_password(password):
    # Security issue: using MD5
    return hashlib.md5(password.encode()).hexdigest()

def verify_password(password, stored_hash):
    return hash_password(password) == stored_hash

def authenticate(username, password):
    # No input validation
    user = get_user(username)
    if user and verify_password(password, user['password']):
        return True
    return False""",
        
        "session.py": """import random

def create_session(user_id):
    # Security issue: predictable session ID
    return f"session_{user_id}_{random.randint(1000, 9999)}"

def validate_session(session_id):
    # No proper validation
    return session_id.startswith("session_")

def destroy_session(session_id):
    # Not implemented
    pass""",
        
        "main.py": """from auth import authenticate
from session import create_session

def login(username, password):
    # No error handling
    if authenticate(username, password):
        session_id = create_session(username)
        return session_id
    return None

def main():
    # Test login
    session = login("admin", "password123")
    print(f"Session: {session}")"""
    }
    
    # Specify locations for focused fixes
    file_locations = {
        "auth.py": [(4, 6), (12, 16)],    # hash_password and authenticate functions
        "session.py": [(4, 6), (8, 10)],  # create_session and validate_session
        "main.py": [(5, 9)]                # login function
    }
    
    print("📁 File contents created in memory (not on disk)")
    print(f"Files: {list(file_contents.keys())}")
    print(f"Total characters: {sum(len(content) for content in file_contents.values())}")
    
    print(f"\n🎯 Targeted locations: {file_locations}")
    
    print(f"\n🚀 Processing with LLM (all in memory)...")
    
    try:
        # Record filesystem state before processing
        original_cwd_files = set(os.listdir('.'))
        
        # Process entirely in memory
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,      # In memory
            file_locations=file_locations,    # In memory
            context_window=3,
            diff_format=True
        )
        
        # Check filesystem state after processing
        final_cwd_files = set(os.listdir('.'))
        
        if result['success']:
            print(f"✅ SUCCESS - All processing completed in memory!")
            print(f"📊 Results:")
            print(f"  - Edited files: {result['edited_files']}")
            print(f"  - Patch size: {len(result['model_patch'])} characters")
            print(f"  - Files changed: {len(result['file_changes'])}")
            
            print(f"\n🔒 Filesystem integrity check:")
            if original_cwd_files == final_cwd_files:
                print(f"  ✅ No new files created in current directory")
            else:
                new_files = final_cwd_files - original_cwd_files
                print(f"  ⚠️  New files detected: {new_files}")
            
            print(f"\n📋 Generated patch preview (first 500 chars):")
            print(result['model_patch'][:500] + "..." if len(result['model_patch']) > 500 else result['model_patch'])
            
            # Demonstrate that file_changes are in memory
            print(f"\n💾 In-memory file changes:")
            for file_path, (original, modified) in result['file_changes'].items():
                print(f"  {file_path}:")
                print(f"    Original: {len(original)} chars")
                print(f"    Modified: {len(modified)} chars")
                print(f"    Changed: {original != modified}")
            
            return True
        else:
            print(f"❌ FAILED: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False


def test_no_temp_files():
    """Test that no temporary files are created during processing"""
    print("\n" + "="*70)
    print("🗂️  TEMPORARY FILES TEST")
    print("="*70)
    
    # Monitor temp directory
    temp_dir = tempfile.gettempdir()
    initial_temp_files = set(os.listdir(temp_dir))
    
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",
        llm_provider="custom",
        api_key="1830498799983480864",
        base_url="https://aigc.sankuai.com/v1/openai/native",
        temperature=0.0,
        max_tokens=1024
    )
    
    # Simple test case
    file_contents = {
        "test.py": """def broken_function():
    return None  # Bug: should return something useful"""
    }
    
    problem = "Fix the function to return a proper value"
    
    print(f"📂 Monitoring temp directory: {temp_dir}")
    print(f"📊 Initial temp files count: {len(initial_temp_files)}")
    
    try:
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            diff_format=True
        )
        
        # Check temp directory after processing
        final_temp_files = set(os.listdir(temp_dir))
        
        if result['success']:
            print(f"✅ Processing successful")
            
            if initial_temp_files == final_temp_files:
                print(f"✅ No temporary files created or left behind")
            else:
                new_temp_files = final_temp_files - initial_temp_files
                print(f"⚠️  New temp files detected: {new_temp_files}")
            
            return len(new_temp_files) == 0 if 'new_temp_files' in locals() else True
        else:
            print(f"❌ Processing failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def test_original_repo_untouched():
    """Test that original repository files are never modified"""
    print("\n" + "="*70)
    print("🛡️  ORIGINAL REPOSITORY PROTECTION TEST")
    print("="*70)
    
    # Create a mock "repository" structure in memory
    original_repo_files = {
        "src/main.py": "# Original main file\nprint('Hello World')",
        "src/utils.py": "# Original utils file\ndef helper(): pass",
        "tests/test_main.py": "# Original test file\nimport unittest"
    }
    
    # Store checksums of original content
    import hashlib
    original_checksums = {
        path: hashlib.md5(content.encode()).hexdigest()
        for path, content in original_repo_files.items()
    }
    
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",
        llm_provider="custom",
        api_key="1830498799983480864",
        base_url="https://aigc.sankuai.com/v1/openai/native",
        temperature=0.0,
        max_tokens=1024
    )
    
    problem = "Add error handling to all functions"
    
    print(f"📁 Original repository structure:")
    for path in original_repo_files:
        print(f"  {path} (checksum: {original_checksums[path][:8]}...)")
    
    try:
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=original_repo_files,  # Pass original files
            diff_format=True
        )
        
        # Verify original files are unchanged
        final_checksums = {
            path: hashlib.md5(content.encode()).hexdigest()
            for path, content in original_repo_files.items()
        }
        
        if result['success']:
            print(f"✅ Processing successful")
            
            all_unchanged = True
            for path in original_repo_files:
                if original_checksums[path] == final_checksums[path]:
                    print(f"  ✅ {path}: UNCHANGED")
                else:
                    print(f"  ❌ {path}: MODIFIED!")
                    all_unchanged = False
            
            if all_unchanged:
                print(f"🛡️  All original files remain untouched!")
            
            return all_unchanged
        else:
            print(f"❌ Processing failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def main():
    """Run all memory-only processing tests"""
    print("🔒 MEMORY-ONLY PROCESSING TEST SUITE")
    print("=" * 70)
    
    print("This test suite verifies that:")
    print("  ✅ All processing happens in memory")
    print("  ✅ No files are saved to disk")
    print("  ✅ No temporary files are created")
    print("  ✅ Original repository remains untouched")
    print("  ✅ Results are returned in memory")
    
    # Run tests
    results = []
    
    # Test 1: Memory-only processing
    results.append(("Memory-Only Processing", test_memory_only_processing()))
    
    # Test 2: No temp files
    results.append(("No Temporary Files", test_no_temp_files()))
    
    # Test 3: Original repo protection
    results.append(("Original Repository Protection", test_original_repo_untouched()))
    
    # Summary
    print("\n" + "="*70)
    print("🔒 MEMORY-ONLY TEST SUMMARY")
    print("="*70)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("🔒 Memory-only processing is fully functional!")
        print("✅ Your original repository is completely safe!")
    else:
        print(f"\n⚠️  {total - passed} tests failed.")
    
    print(f"\n📋 Key Guarantees:")
    print(f"  🔒 No files written to disk")
    print(f"  🗂️  No temporary files created")
    print(f"  🛡️  Original repository untouched")
    print(f"  💾 All results in memory")
    print(f"  🚀 Full LLM processing capability")


if __name__ == "__main__":
    main()
