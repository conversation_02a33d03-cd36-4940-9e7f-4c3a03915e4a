diff --git a/auth.py b/auth.py
index 77085ae..b0ef3eb 100644
--- a/auth.py
+++ b/auth.py
@@ -1,25 +1,38 @@
 # Authentication module
 import hashlib
 import uuid
+import hashlib
+import secrets
+import re
 
 def hash_password(password):
-    # Lines 5-7: Bug - using insecure MD5
-    return hashlib.md5(password.encode()).hexdigest()
+    # Use secure bcrypt-style hashing with salt
+    import bcrypt
+    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
 
 def create_user(username, password):
     if not username:
         return None
-    # Lines 12-14: Bug - no proper validation
+    # Proper input validation for username
+    if not isinstance(username, str) or len(username.strip()) == 0:
+        return None
+    if len(username) > 50:  # Reasonable length limit
+        return None
+    if not re.match(r'^[a-zA-Z0-9_.-]+$', username):  # Only allow safe characters
+        return None
+    if not password or len(password) < 8:  # Minimum password length
+        return None
+    
     hashed_pwd = hash_password(password)
-    return {"username": username, "password": hashed_pwd}
+    return {"username": username.strip(), "password": hashed_pwd}
 
 def authenticate(username, password):
     user = get_user(username)
     return user and user["password"] == hash_password(password)
 
 def create_session(user_id):
-    # Lines 20-22: Bug - predictable session ID
-    session_id = str(user_id) + "_session"
+    # Generate cryptographically secure random session ID
+    session_id = secrets.token_urlsafe(32)
     return session_id
 
 def validate_session(session_id):
