diff --git a/calculator.py b/calculator.py
index 653a7bf..ab1ca3e 100644
--- a/calculator.py
+++ b/calculator.py
@@ -8,7 +8,9 @@ def multiply(a, b):
     return a * b
 
 def divide(a, b):
-    return a / b  # Bug: no zero check
+    if b == 0:
+        raise ZeroDivisionError("Cannot divide by zero")
+    return a / b
 
 def power(a, b):
     return a ** b
\ No newline at end of file
diff --git a/main.py b/main.py
index 5cff1d7..8d80d85 100644
--- a/main.py
+++ b/main.py
@@ -7,8 +7,16 @@ def main():
     print(f"Addition: {add(a, b)}")
     print(f"Subtraction: {subtract(a, b)}")
     print(f"Multiplication: {multiply(a, b)}")
-    print(f"Division: {divide(a, b)}")  # Bug: will crash here
-    print(f"Power: {power(a, b)}")
+    
+    try:
+        print(f"Division: {divide(a, b)}")
+    except ZeroDivisionError as e:
+        print(f"Division: Error - {e}")
+    
+    try:
+        print(f"Power: {power(a, b)}")
+    except Exception as e:
+        print(f"Power: Error - {e}")
 
 if __name__ == "__main__":
     main()
\ No newline at end of file
