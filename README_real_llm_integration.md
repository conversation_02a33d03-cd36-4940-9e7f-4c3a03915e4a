# Real LLM Integration for Agentless Multi-file Patch Generator

这个文档说明如何在`agentless_multifile_patch.py`中使用真实的LLM API调用，而不是模拟调用。

## 🚀 新增功能

### 1. **真实LLM调用**
- 支持OpenAI、Anthropic、DeepSeek等多个LLM提供商
- 自动速率限制和错误重试机制
- 完整的token使用统计和日志记录

### 2. **统一LLM客户端**
- `llm_client.py` - 统一的LLM调用接口
- 支持同步和异步调用
- 自动错误处理和重试机制

### 3. **配置灵活性**
- 支持环境变量和直接传参配置API密钥
- 可配置温度、最大token数、上下文长度等参数
- 支持自定义API端点

## 📦 安装依赖

```bash
# 基础依赖
pip install openai anthropic

# 可选：用于异步处理
pip install nest-asyncio
```

## 🔧 配置方式

### 方式1：环境变量配置

```bash
# OpenAI
export OPENAI_API_KEY="your-openai-api-key"

# Anthropic
export ANTHROPIC_API_KEY="your-anthropic-api-key"

# DeepSeek
export DEEPSEEK_API_KEY="your-deepseek-api-key"
```

### 方式2：代码中直接配置

```python
from agentless_multifile_patch import AgentlessMultifilePatchGenerator

generator = AgentlessMultifilePatchGenerator(
    model_name="gpt-4",
    llm_provider="openai",
    api_key="your-api-key",
    temperature=0.0,
    max_tokens=4096
)
```

### 方式3：命令行参数

```bash
python agentless_multifile_patch.py \
    --problem "Fix the bugs" \
    --files file1.py file2.py \
    --provider openai \
    --model gpt-4 \
    --api-key $OPENAI_API_KEY \
    --temperature 0.0 \
    --max-tokens 4096
```

## 🎯 支持的LLM提供商

### 1. **OpenAI**
```python
generator = AgentlessMultifilePatchGenerator(
    model_name="gpt-4",  # 或 "gpt-4-turbo", "gpt-3.5-turbo"
    llm_provider="openai",
    api_key=os.getenv("OPENAI_API_KEY")
)
```

### 2. **Anthropic Claude**
```python
generator = AgentlessMultifilePatchGenerator(
    model_name="claude-3-sonnet-20240229",  # 或 "claude-3-opus-20240229"
    llm_provider="anthropic",
    api_key=os.getenv("ANTHROPIC_API_KEY")
)
```

### 3. **DeepSeek**
```python
generator = AgentlessMultifilePatchGenerator(
    model_name="deepseek-coder",
    llm_provider="deepseek",
    api_key=os.getenv("DEEPSEEK_API_KEY")
)
```

### 4. **自定义API**
```python
generator = AgentlessMultifilePatchGenerator(
    model_name="custom-model",
    llm_provider="custom",
    api_key="your-api-key",
    base_url="https://your-api.com/v1"
)
```

## 📝 使用示例

### 基本使用

```python
from agentless_multifile_patch import AgentlessMultifilePatchGenerator

# 初始化生成器
generator = AgentlessMultifilePatchGenerator(
    model_name="gpt-4",
    llm_provider="openai",
    api_key="your-api-key"
)

# 定义问题和文件
problem = "修复除零错误并添加异常处理"
file_contents = {
    "calculator.py": "def divide(a, b):\n    return a / b",
    "main.py": "result = divide(10, 0)"
}

# 生成补丁
result = generator.generate_multifile_patch(
    problem_statement=problem,
    file_contents=file_contents,
    diff_format=True
)

if result['success']:
    print(result['model_patch'])
else:
    print(f"Error: {result['error']}")
```

### 高级配置

```python
generator = AgentlessMultifilePatchGenerator(
    model_name="claude-3-opus-20240229",
    llm_provider="anthropic",
    api_key=os.getenv("ANTHROPIC_API_KEY"),
    temperature=0.1,
    max_tokens=8192,
    max_context_length=15000,
    max_requests_per_minute=20,
    max_retries=5,
    timeout=180
)
```

## 🔄 工作流程

1. **初始化LLM客户端**：根据配置创建相应的LLM客户端
2. **构造多文件上下文**：将多个文件组合成统一的上下文
3. **调用真实LLM**：发送请求到配置的LLM服务
4. **解析响应**：解析LLM返回的编辑命令
5. **生成补丁**：应用编辑并生成Git diff格式补丁

## 📊 日志和监控

系统会自动记录：
- LLM调用状态和响应时间
- Token使用统计（输入/输出/总计）
- 错误信息和重试次数
- 速率限制状态

```
2024-01-15 10:30:15 - INFO - Initialized openai client with model gpt-4
2024-01-15 10:30:16 - INFO - Calling openai LLM with 2048 characters
2024-01-15 10:30:18 - INFO - LLM call successful, received 1024 characters
2024-01-15 10:30:18 - INFO - Token usage - Prompt: 512, Completion: 256, Total: 768
```

## ⚡ 性能优化

### 1. **速率限制**
- 自动管理API调用频率
- 可配置每分钟最大请求数
- 智能等待和重试机制

### 2. **错误处理**
- 自动重试失败的请求
- 指数退避策略
- 降级到模拟调用作为后备

### 3. **上下文管理**
- 自动调整文件数量以适应模型限制
- 智能截断长文件内容
- 优先保留重要代码段

## 🛠️ 故障排除

### 常见问题

1. **API密钥错误**
```
Error: Invalid API key
解决：检查API密钥是否正确设置
```

2. **速率限制**
```
Rate limit reached, waiting 30.5 seconds...
解决：这是正常行为，系统会自动等待
```

3. **上下文长度超限**
```
Context length exceeded
解决：系统会自动减少文件数量，或手动设置较小的max_context_length
```

4. **网络连接问题**
```
API connection error
解决：检查网络连接，系统会自动重试
```

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

generator = AgentlessMultifilePatchGenerator(...)
# 现在会显示详细的调试信息
```

## 📁 文件结构

```
├── agentless_multifile_patch.py    # 主要实现（已更新支持真实LLM）
├── llm_client.py                   # 统一LLM客户端
├── example_real_llm_usage.py       # 真实LLM使用示例
├── llm_config_examples.py          # 配置示例
└── README_real_llm_integration.md  # 本文档
```

## 🎉 总结

通过集成真实的LLM API调用，Agentless多文件补丁生成器现在可以：

- ✅ 使用真实的LLM进行代码分析和修复
- ✅ 支持多个主流LLM提供商
- ✅ 自动处理速率限制和错误重试
- ✅ 提供详细的使用统计和日志
- ✅ 保持与原有API的完全兼容性
- ✅ 在LLM不可用时自动降级到模拟模式

这使得系统能够在实际生产环境中使用，同时保持了开发和测试的灵活性。
