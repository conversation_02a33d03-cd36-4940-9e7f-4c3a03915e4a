# Chunk to Patch Generator

这个模块实现了从有问题的代码块生成模型补丁的功能。它接受问题描述和相关代码块作为输入，使用LLM生成SEARCH/REPLACE格式的编辑，并输出Git diff格式的补丁。

## 功能特性

- **输入**: 问题描述 + 有问题的代码块
- **输出**: Git diff格式的model_patch
- **LLM格式**: SEARCH/REPLACE格式
- **语法检查**: 自动验证生成代码的语法正确性
- **Git集成**: 使用临时Git仓库生成标准diff格式

## 文件结构

```
chunk_to_patch.py          # 主要实现文件
test_chunk_to_patch.py     # 测试脚本
README_chunk_to_patch.md   # 说明文档
```

## 核心类: ChunkToPatchGenerator

### 主要方法

1. **`generate_patch()`** - 主要接口方法
2. **`extract_python_blocks()`** - 从LLM输出提取Python代码块
3. **`parse_search_replace_commands()`** - 解析SEARCH/REPLACE命令
4. **`apply_search_replace_edits()`** - 应用编辑到文件内容
5. **`create_git_diff()`** - 生成Git diff格式补丁

## 使用方法

### 1. 命令行使用

```bash
python chunk_to_patch.py \
    --problem "函数应该返回问候消息但目前返回None" \
    --chunk "def greet():\n    return None" \
    --file "greetings.py" \
    --output "patch.diff"
```

### 2. Python API使用

```python
from chunk_to_patch import ChunkToPatchGenerator

# 初始化生成器
generator = ChunkToPatchGenerator(model_name="gpt-4")

# 定义问题和代码块
problem = "函数应该返回问候消息但目前返回None"
code_chunk = """def greet():
    return None"""
file_path = "greetings.py"

# 生成补丁
result = generator.generate_patch(
    problem_statement=problem,
    code_chunk=code_chunk,
    file_path=file_path
)

if result['success']:
    print("生成的补丁:")
    print(result['model_patch'])
else:
    print("错误:", result['error'])
```

## LLM输出格式

LLM需要输出SEARCH/REPLACE格式的编辑命令：

```python
### file_path.py
<<<<<<< SEARCH
原始代码块
=======
修改后的代码块
>>>>>>> REPLACE
```

### 示例

```python
### greetings.py
<<<<<<< SEARCH
def greet():
    return None
=======
def greet():
    return "Hello, World!"
>>>>>>> REPLACE
```

## 输出格式

成功时返回的字典包含：

```python
{
    "success": True,
    "model_patch": "Git diff格式的补丁",
    "raw_output": "LLM原始输出",
    "original_content": "原始文件内容",
    "new_content": "修改后的文件内容",
    "edited_files": ["文件路径列表"],
    "commands": [{"search": "...", "replace": "..."}]
}
```

失败时返回：

```python
{
    "success": False,
    "error": "错误描述",
    "raw_output": "LLM原始输出"
}
```

## 运行测试

```bash
python test_chunk_to_patch.py
```

测试包括：
1. 基本功能测试
2. 完整文件内容测试
3. SEARCH/REPLACE解析测试
4. 多重编辑测试
5. Git diff生成测试

## 工作流程

1. **输入处理**: 接收问题描述、代码块和文件路径
2. **提示构造**: 使用模板构造LLM提示
3. **LLM调用**: 获取SEARCH/REPLACE格式的编辑命令
4. **命令解析**: 提取并解析Python代码块中的编辑命令
5. **编辑应用**: 将SEARCH/REPLACE命令应用到原始内容
6. **语法检查**: 验证生成代码的语法正确性
7. **补丁生成**: 使用临时Git仓库生成标准diff格式
8. **结果返回**: 返回包含补丁和元数据的结果字典

## 注意事项

1. **缩进敏感**: SEARCH/REPLACE格式要求精确的缩进匹配
2. **LLM集成**: 当前使用模拟LLM调用，需要替换为实际API调用
3. **错误处理**: 包含语法检查和匹配失败的错误处理
4. **临时文件**: 使用临时目录避免文件冲突

## 扩展建议

1. **LLM集成**: 集成OpenAI、Anthropic等API
2. **多文件支持**: 扩展支持多文件编辑
3. **上下文窗口**: 添加更多上下文信息
4. **缓存机制**: 添加LLM响应缓存
5. **配置文件**: 支持配置文件设置

## 依赖项

- Python 3.7+
- ast (内置)
- subprocess (内置)
- tempfile (内置)
- uuid (内置)
- re (内置)
- json (内置)
- argparse (内置)

无需额外安装第三方依赖。
