#!/usr/bin/env python3
"""
Configuration for your specific API setup

This file contains configuration and helper functions for using your API
with the AgentlessMultifilePatchGenerator.
"""

import os
from agentless_multifile_patch import AgentlessMultifilePatchGenerator


# Your API configuration
YOUR_API_CONFIG = {
    "api_key": "1830498799983480864",
    "base_url": "https://aigc.sankuai.com/v1/openai/native",
    "model": "anthropic.claude-sonnet-4",
    "provider": "custom",
    "max_requests_per_minute": 50,
    "concurrency_limit": 5,
    "timeout": 120
}


def create_your_api_generator(**overrides):
    """
    Create AgentlessMultifilePatchGenerator with your API configuration
    
    Args:
        **overrides: Override any default configuration values
    
    Returns:
        AgentlessMultifilePatchGenerator instance
    """
    config = YOUR_API_CONFIG.copy()
    config.update(overrides)
    
    return AgentlessMultifilePatchGenerator(
        model_name=config["model"],
        llm_provider=config["provider"],
        api_key=config["api_key"],
        base_url=config["base_url"],
        temperature=config.get("temperature", 0.0),
        max_tokens=config.get("max_tokens", 4096),
        max_context_length=config.get("max_context_length", 8000),
        max_requests_per_minute=config["max_requests_per_minute"],
        max_retries=config.get("max_retries", 3),
        timeout=config["timeout"]
    )


def quick_fix(problem, file_contents, diff_format=True, **generator_overrides):
    """
    Quick fix function using your API
    
    Args:
        problem: Problem description
        file_contents: Dictionary of file_path -> content
        diff_format: Whether to use SEARCH/REPLACE format
        **generator_overrides: Override generator configuration
    
    Returns:
        Result dictionary
    """
    generator = create_your_api_generator(**generator_overrides)
    
    return generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,
        diff_format=diff_format
    )


def batch_process_with_your_api(problems_and_files, output_dir="./patches"):
    """
    Process multiple problem/file combinations using your API
    
    Args:
        problems_and_files: List of (problem, file_contents) tuples
        output_dir: Directory to save patches
    
    Returns:
        List of results
    """
    import os
    os.makedirs(output_dir, exist_ok=True)
    
    generator = create_your_api_generator()
    results = []
    
    for i, (problem, file_contents) in enumerate(problems_and_files):
        print(f"Processing batch {i+1}/{len(problems_and_files)}...")
        
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            diff_format=True
        )
        
        if result['success']:
            # Save patch
            patch_file = os.path.join(output_dir, f"patch_{i+1}.diff")
            with open(patch_file, 'w') as f:
                f.write(result['model_patch'])
            result['patch_file'] = patch_file
            print(f"  ✓ Patch saved to {patch_file}")
        else:
            print(f"  ✗ Failed: {result['error']}")
        
        results.append(result)
    
    return results


# Example usage functions
def example_calculator_fix():
    """Example: Fix calculator division by zero"""
    problem = "Fix division by zero error and add proper error handling"
    
    file_contents = {
        "calculator.py": """def divide(a, b):
    return a / b  # Bug: no zero check""",
        
        "main.py": """from calculator import divide

def main():
    result = divide(10, 0)  # Will crash
    print(result)"""
    }
    
    return quick_fix(problem, file_contents)


def example_auth_security():
    """Example: Fix authentication security issues"""
    problem = """
    Fix security vulnerabilities:
    1. Hash passwords properly
    2. Add input validation
    3. Handle authentication errors
    """
    
    file_contents = {
        "auth.py": """def hash_password(password):
    return password  # Bug: no hashing

def verify_password(password, stored):
    return password == stored  # Bug: plain text""",
        
        "user.py": """def create_user(username, password):
    # Bug: no validation
    return {'username': username, 'password': password}""",
        
        "main.py": """def login(username, password):
    # Bug: no error handling
    if authenticate(username, password):
        return True
    return False"""
    }
    
    return quick_fix(problem, file_contents)


def example_api_validation():
    """Example: Fix API input validation"""
    problem = """
    The API endpoints need proper input validation and error handling:
    1. Validate JSON input format
    2. Check required fields
    3. Return proper error responses
    """
    
    file_contents = {
        "api.py": """from flask import Flask, request, jsonify

@app.route('/users', methods=['POST'])
def create_user():
    data = request.get_json()  # Bug: no validation
    username = data['username']  # Bug: no key check
    return jsonify({'id': 1})""",
        
        "validators.py": """def validate_user_data(data):
    # Bug: not implemented
    return True""",
        
        "errors.py": """def handle_validation_error(error):
    # Bug: not implemented
    return "Error", 400"""
    }
    
    return quick_fix(problem, file_contents)


def run_examples():
    """Run all example fixes"""
    print("Running examples with your API...")
    
    examples = [
        ("Calculator Fix", example_calculator_fix),
        ("Auth Security", example_auth_security),
        ("API Validation", example_api_validation)
    ]
    
    for name, example_func in examples:
        print(f"\n{'='*50}")
        print(f"EXAMPLE: {name}")
        print('='*50)
        
        try:
            result = example_func()
            
            if result['success']:
                print("✓ Success!")
                print(f"Edited files: {result['edited_files']}")
                print("\nGenerated patch (first 300 chars):")
                print(result['model_patch'][:300] + "...")
            else:
                print(f"✗ Failed: {result['error']}")
                
        except Exception as e:
            print(f"✗ Exception: {e}")


def test_api_connection():
    """Test basic API connection"""
    print("Testing API connection...")
    
    try:
        generator = create_your_api_generator()
        
        # Simple test
        result = generator.generate_multifile_patch(
            problem_statement="Fix the function to return 'Hello' instead of None",
            file_contents={"test.py": "def greet():\n    return None"},
            diff_format=True
        )
        
        if result['success']:
            print("✓ API connection successful!")
            return True
        else:
            print(f"✗ API test failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"✗ API connection failed: {e}")
        return False


if __name__ == "__main__":
    print("Your API Configuration Test")
    print("=" * 50)
    
    print("Configuration:")
    for key, value in YOUR_API_CONFIG.items():
        if key == "api_key":
            print(f"  {key}: {value[:10]}...")  # Hide most of the API key
        else:
            print(f"  {key}: {value}")
    
    print("\nTesting API connection...")
    if test_api_connection():
        print("\nRunning examples...")
        run_examples()
    else:
        print("\nAPI connection failed. Please check your configuration.")
