# Agentless Multi-file Patch Generator

这个模块完全模拟了Agentless的逻辑，用于从多个文件生成补丁。它接受问题描述和多个文件内容作为输入，使用LLM在单次调用中生成多文件编辑命令，并输出Git diff格式的补丁。

## 核心特性

- **多文件单次处理**: 模拟Agentless的多文件一次性LLM调用机制
- **上下文窗口管理**: 自动管理上下文长度，必要时减少文件数量
- **多种编辑格式**: 支持edit_file、SEARCH/REPLACE和str_replace格式
- **位置感知处理**: 支持指定文件中的特定位置进行重点处理
- **Git diff输出**: 为所有修改的文件生成标准Git diff格式补丁

## 文件结构

```
agentless_multifile_patch.py     # 主要实现文件
test_agentless_multifile.py      # 测试脚本
README_agentless_multifile.md    # 说明文档
```

## 核心类: AgentlessMultifilePatchGenerator

### 主要方法

1. **`generate_multifile_patch()`** - 主要接口方法
2. **`construct_multifile_context()`** - 构造多文件上下文
3. **`split_edit_multifile_commands()`** - 按文件分组编辑命令
4. **`parse_edit_commands()`** - 解析edit_file命令
5. **`parse_search_replace_commands()`** - 解析SEARCH/REPLACE命令
6. **`create_git_diff()`** - 生成多文件Git diff补丁

## 使用方法

### 1. Python API使用

```python
from agentless_multifile_patch import AgentlessMultifilePatchGenerator

# 初始化生成器
generator = AgentlessMultifilePatchGenerator(
    model_name="gpt-4",
    max_context_length=8000
)

# 定义问题和多个文件
problem = "修复计算器模块中的除零错误，同时修复主文件中的异常处理"

file_contents = {
    "calculator.py": """def divide(a, b):
    return a / b  # Bug: no zero check""",
    
    "main.py": """from calculator import divide
def main():
    result = divide(10, 0)  # Bug: will crash
    print(result)"""
}

# 生成多文件补丁
result = generator.generate_multifile_patch(
    problem_statement=problem,
    file_contents=file_contents
)

if result['success']:
    print("生成的多文件补丁:")
    print(result['model_patch'])
    print(f"修改的文件: {result['edited_files']}")
```

### 2. 使用SEARCH/REPLACE格式

```python
result = generator.generate_multifile_patch(
    problem_statement=problem,
    file_contents=file_contents,
    diff_format=True  # 使用SEARCH/REPLACE格式
)
```

### 3. 指定文件位置

```python
# 指定需要重点关注的代码位置
file_locations = {
    "calculator.py": [(2, 2)],  # 第2行的divide函数
    "main.py": [(3, 3)]         # 第3行的divide调用
}

result = generator.generate_multifile_patch(
    problem_statement=problem,
    file_contents=file_contents,
    file_locations=file_locations,
    context_window=5
)
```

### 4. 命令行使用

```bash
python3 agentless_multifile_patch.py \
    --problem "修复多文件中的bug" \
    --files file1.py file2.py file3.py \
    --diff-format \
    --output patch.diff
```

## 工作流程（完全模拟Agentless）

### 1. **多文件上下文构造**
```python
def construct_multifile_context(file_contents, file_locations, context_window):
    topn_content = ""
    for file_path, content in file_contents.items():
        # 提取相关位置的上下文
        file_content = extract_context_with_line_numbers(content, locations)
        topn_content += f"### {file_path}\n{file_content}\n\n\n"
    return topn_content
```

### 2. **上下文长度管理**
```python
def _manage_context_length(file_contents, problem_statement):
    while len(file_contents) > 1:
        if estimate_token_count(message) <= max_context_length:
            break
        # 移除优先级最低的文件
        remove_last_file()
    return managed_file_contents
```

### 3. **单次LLM调用**
```python
message = prompt_template.format(
    problem_statement=problem_statement,
    content=topn_content  # 包含所有文件的组合内容
)
llm_output = llm_call(message)  # 只调用一次LLM
```

### 4. **多文件命令解析**
```python
# LLM输出包含对多个文件的编辑
commands = extract_python_blocks(llm_output)
file_to_commands = split_edit_multifile_commands(commands)
# 结果: {"file1.py": [commands], "file2.py": [commands]}
```

### 5. **应用编辑并生成补丁**
```python
file_changes = {}
for file_path, commands in file_to_commands.items():
    new_content = apply_edits(original_content, commands)
    file_changes[file_path] = (original_content, new_content)

git_diff = create_git_diff(file_changes)  # 生成统一的Git diff
```

## 输入格式

### LLM接收的输入格式：
```
We are currently solving the following issue within our repository. Here is the issue text:
--- BEGIN ISSUE ---
{problem_statement}
--- END ISSUE ---

Below are some code segments, each from a relevant file. One or more of these files may contain bugs.
--- BEGIN FILE ---
```
### file1.py
   1: def function1():
   2:     return "old"
   3: 

### file2.py
   1: from file1 import function1
   2: def main():
   3:     print(function1())
```
--- END FILE ---

Please generate `edit_file` commands to fix the issue.
```

## 输出格式

### 成功时返回：
```python
{
    "success": True,
    "model_patch": "Git diff格式的多文件补丁",
    "raw_output": "LLM原始输出",
    "edited_files": ["file1.py", "file2.py"],
    "new_contents": ["修改后的file1内容", "修改后的file2内容"],
    "original_contents": ["原始file1内容", "原始file2内容"],
    "file_changes": {"file1.py": (原始内容, 新内容), ...}
}
```

## 与原始chunk_to_patch.py的区别

| 特性 | chunk_to_patch.py | agentless_multifile_patch.py |
|------|------------------|------------------------------|
| 文件处理 | 单文件单次处理 | 多文件单次处理 |
| LLM调用 | 每个chunk一次调用 | 所有文件一次调用 |
| 上下文管理 | 简单的单文件上下文 | 复杂的多文件上下文管理 |
| 位置感知 | 基本的代码块定位 | 精确的行号和上下文窗口 |
| 输出格式 | 单文件Git diff | 多文件统一Git diff |

## 运行测试

```bash
python3 test_agentless_multifile.py
```

测试包括：
1. 基本多文件处理
2. SEARCH/REPLACE格式
3. 上下文窗口管理
4. 文件位置处理
5. 上下文构造

## 优势

1. **效率高**: 单次LLM调用处理多文件
2. **上下文完整**: LLM能看到文件间的依赖关系
3. **一致性好**: 确保跨文件修改的协调性
4. **成本低**: 减少API调用次数
5. **智能管理**: 自动处理上下文长度限制

## 扩展建议

1. **真实LLM集成**: 替换模拟调用为真实API
2. **更多编辑格式**: 支持更多编辑命令格式
3. **增量处理**: 支持大型代码库的增量处理
4. **缓存机制**: 添加上下文和响应缓存
5. **并行处理**: 支持多实例并行处理

这个实现完全模拟了Agentless的多文件处理逻辑，提供了与原系统相同的功能和工作流程。
