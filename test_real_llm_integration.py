#!/usr/bin/env python3
"""
Test script for real LLM integration

This script tests the AgentlessMultifilePatchGenerator with real LLM calls
and fallback to simulation when LLM is not available.
"""

import os
import sys
from agentless_multifile_patch import AgentlessMultifilePatchGenerator


def test_simulation_fallback():
    """Test that simulation works when no API key is provided"""
    print("="*60)
    print("TEST 1: Simulation Fallback")
    print("="*60)
    
    generator = AgentlessMultifilePatchGenerator(
        model_name="gpt-4",
        llm_provider="openai",
        api_key=None,  # No API key - should fall back to simulation
        temperature=0.0
    )
    
    problem = "Fix the function to return a greeting instead of None"
    file_contents = {
        "greetings.py": """def greet():
    return None  # Bug: should return greeting"""
    }
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,
        diff_format=True
    )
    
    print(f"Result success: {result['success']}")
    if result['success']:
        print(f"Edited files: {result.get('edited_files', [])}")
        print("Generated patch (first 200 chars):")
        print(result['model_patch'][:200] + "..." if len(result['model_patch']) > 200 else result['model_patch'])
    else:
        print(f"Error: {result['error']}")
    
    return result['success']


def test_openai_integration():
    """Test OpenAI integration if API key is available"""
    print("\n" + "="*60)
    print("TEST 2: OpenAI Integration")
    print("="*60)
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("SKIPPED: No OPENAI_API_KEY environment variable found")
        return True
    
    try:
        generator = AgentlessMultifilePatchGenerator(
            model_name="gpt-3.5-turbo",  # Use cheaper model for testing
            llm_provider="openai",
            api_key=api_key,
            temperature=0.0,
            max_tokens=1024
        )
        
        problem = "Fix the divide function to handle division by zero"
        file_contents = {
            "calculator.py": """def divide(a, b):
    return a / b  # Bug: no zero check"""
        }
        
        print("Calling OpenAI API...")
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            diff_format=True
        )
        
        print(f"Result success: {result['success']}")
        if result['success']:
            print(f"Edited files: {result.get('edited_files', [])}")
            print("Generated patch (first 300 chars):")
            print(result['model_patch'][:300] + "..." if len(result['model_patch']) > 300 else result['model_patch'])
        else:
            print(f"Error: {result['error']}")
        
        return result['success']
        
    except Exception as e:
        print(f"Exception during OpenAI test: {e}")
        return False


def test_anthropic_integration():
    """Test Anthropic integration if API key is available"""
    print("\n" + "="*60)
    print("TEST 3: Anthropic Integration")
    print("="*60)
    
    api_key = os.getenv("ANTHROPIC_API_KEY")
    if not api_key:
        print("SKIPPED: No ANTHROPIC_API_KEY environment variable found")
        return True
    
    try:
        generator = AgentlessMultifilePatchGenerator(
            model_name="claude-3-haiku-20240307",  # Use cheaper model for testing
            llm_provider="anthropic",
            api_key=api_key,
            temperature=0.0,
            max_tokens=1024
        )
        
        problem = "Add input validation to the user creation function"
        file_contents = {
            "user.py": """def create_user(username, email):
    # Bug: no input validation
    user = {'username': username, 'email': email}
    return user"""
        }
        
        print("Calling Anthropic API...")
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            diff_format=True
        )
        
        print(f"Result success: {result['success']}")
        if result['success']:
            print(f"Edited files: {result.get('edited_files', [])}")
            print("Generated patch (first 300 chars):")
            print(result['model_patch'][:300] + "..." if len(result['model_patch']) > 300 else result['model_patch'])
        else:
            print(f"Error: {result['error']}")
        
        return result['success']
        
    except Exception as e:
        print(f"Exception during Anthropic test: {e}")
        return False


def test_multifile_scenario():
    """Test multi-file scenario with available LLM"""
    print("\n" + "="*60)
    print("TEST 4: Multi-file Scenario")
    print("="*60)
    
    # Try to use any available API key
    api_key = None
    provider = None
    model = None
    
    if os.getenv("OPENAI_API_KEY"):
        api_key = os.getenv("OPENAI_API_KEY")
        provider = "openai"
        model = "gpt-3.5-turbo"
    elif os.getenv("ANTHROPIC_API_KEY"):
        api_key = os.getenv("ANTHROPIC_API_KEY")
        provider = "anthropic"
        model = "claude-3-haiku-20240307"
    
    if not api_key:
        print("Using simulation (no API keys available)")
        provider = "openai"
        model = "gpt-4"
    
    try:
        generator = AgentlessMultifilePatchGenerator(
            model_name=model,
            llm_provider=provider,
            api_key=api_key,
            temperature=0.0,
            max_tokens=2048
        )
        
        problem = """
        Fix the authentication system:
        1. Hash passwords properly in auth.py
        2. Add error handling in main.py
        """
        
        file_contents = {
            "auth.py": """def hash_password(password):
    # Bug: no actual hashing
    return password

def verify_password(password, stored):
    return password == stored""",
            
            "main.py": """from auth import hash_password, verify_password

def login(username, password):
    # Bug: no error handling
    stored_password = get_stored_password(username)
    if verify_password(password, stored_password):
        return True
    return False

def get_stored_password(username):
    # Mock function
    return "plain_password" """
        }
        
        print(f"Using {provider} with model {model}")
        if api_key:
            print("Making real LLM call...")
        else:
            print("Using simulation...")
            
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            diff_format=True
        )
        
        print(f"Result success: {result['success']}")
        if result['success']:
            print(f"Edited files: {result.get('edited_files', [])}")
            print("Generated patch (first 400 chars):")
            print(result['model_patch'][:400] + "..." if len(result['model_patch']) > 400 else result['model_patch'])
        else:
            print(f"Error: {result['error']}")
        
        return result['success']
        
    except Exception as e:
        print(f"Exception during multi-file test: {e}")
        return False


def main():
    """Run all tests"""
    print("Real LLM Integration Test Suite")
    print("=" * 60)
    
    # Check available API keys
    has_openai = bool(os.getenv("OPENAI_API_KEY"))
    has_anthropic = bool(os.getenv("ANTHROPIC_API_KEY"))
    
    print(f"Available API keys:")
    print(f"  OpenAI: {'✓' if has_openai else '✗'}")
    print(f"  Anthropic: {'✓' if has_anthropic else '✗'}")
    
    if not has_openai and not has_anthropic:
        print("\nNote: No API keys found. Tests will use simulation fallback.")
    
    # Run tests
    results = []
    
    # Test 1: Simulation fallback
    results.append(("Simulation Fallback", test_simulation_fallback()))
    
    # Test 2: OpenAI integration
    results.append(("OpenAI Integration", test_openai_integration()))
    
    # Test 3: Anthropic integration
    results.append(("Anthropic Integration", test_anthropic_integration()))
    
    # Test 4: Multi-file scenario
    results.append(("Multi-file Scenario", test_multifile_scenario()))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
