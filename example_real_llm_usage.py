#!/usr/bin/env python3
"""
Example usage of agentless_multifile_patch.py with real LLM calls

This script demonstrates how to use the AgentlessMultifilePatchGenerator
with real LLM APIs instead of simulation.
"""

import os
import json
from agentless_multifile_patch import AgentlessMultifilePatchGenerator


def example_openai_multifile_fix():
    """Example using OpenAI API for multi-file bug fixing"""
    print("="*70)
    print("EXAMPLE: OpenAI Multi-file Bug Fix")
    print("="*70)
    
    # Initialize generator with OpenAI
    generator = AgentlessMultifilePatchGenerator(
        model_name="gpt-4",
        llm_provider="openai",
        api_key=os.getenv("OPENAI_API_KEY"),  # Set your API key
        temperature=0.0,
        max_tokens=4096
    )
    
    problem = """
    There's a critical bug in the calculator module where division by zero is not handled properly.
    The main.py file calls the calculator functions but doesn't handle the exceptions that can be raised.
    Both files need to be fixed:
    1. calculator.py should raise appropriate exceptions for invalid operations
    2. main.py should handle these exceptions gracefully
    """
    
    file_contents = {
        "calculator.py": """def add(a, b):
    return a + b

def subtract(a, b):
    return a - b

def multiply(a, b):
    return a * b

def divide(a, b):
    return a / b  # Bug: no zero check

def power(a, b):
    return a ** b""",
        
        "main.py": """from calculator import add, subtract, multiply, divide, power

def main():
    a, b = 10, 0
    
    print("Calculator Results:")
    print(f"Addition: {add(a, b)}")
    print(f"Subtraction: {subtract(a, b)}")
    print(f"Multiplication: {multiply(a, b)}")
    print(f"Division: {divide(a, b)}")  # Bug: will crash here
    print(f"Power: {power(a, b)}")

if __name__ == "__main__":
    main()"""
    }
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,
        diff_format=True  # Use SEARCH/REPLACE format
    )
    
    print("Problem:", problem.strip())
    print(f"\nFiles to process: {list(file_contents.keys())}")
    
    if result['success']:
        print(f"\nEdited files: {result['edited_files']}")
        print("\nGenerated multi-file patch:")
        print(result['model_patch'])
        
        # Save patch to file
        with open("openai_calculator_fix.patch", "w") as f:
            f.write(result['model_patch'])
        print("\nPatch saved to: openai_calculator_fix.patch")
    else:
        print(f"Error: {result['error']}")


def example_anthropic_multifile_fix():
    """Example using Anthropic Claude API for multi-file bug fixing"""
    print("\n" + "="*70)
    print("EXAMPLE: Anthropic Claude Multi-file Bug Fix")
    print("="*70)
    
    # Initialize generator with Anthropic
    generator = AgentlessMultifilePatchGenerator(
        model_name="claude-3-sonnet-20240229",
        llm_provider="anthropic",
        api_key=os.getenv("ANTHROPIC_API_KEY"),  # Set your API key
        temperature=0.0,
        max_tokens=4096
    )
    
    problem = """
    The authentication system has security vulnerabilities:
    1. Passwords are stored in plain text
    2. No input validation for user registration
    3. Authentication errors are not handled properly
    Fix these issues across all related files.
    """
    
    file_contents = {
        "auth.py": """def hash_password(password):
    # Bug: no actual hashing
    return password

def verify_password(password, stored):
    # Bug: plain text comparison
    return password == stored

def authenticate(username, password):
    # Simplified auth logic
    user = get_user(username)
    if user and verify_password(password, user['password']):
        return True
    return False""",
        
        "user.py": """from auth import hash_password

def create_user(username, password, email):
    # Bug: no input validation
    user = {
        'username': username,
        'password': hash_password(password),  # Bug: not actually hashed
        'email': email
    }
    save_user(user)
    return user

def get_user(username):
    # Mock implementation
    return {'username': username, 'password': 'plain_password'}

def save_user(user):
    # Mock implementation
    pass""",
        
        "main.py": """from auth import authenticate
from user import create_user

def login(username, password):
    # Bug: no error handling
    if authenticate(username, password):
        print("Login successful")
        return True
    else:
        print("Login failed")
        return False

def register(username, password, email):
    # Bug: no error handling
    user = create_user(username, password, email)
    print(f"User {username} created")
    return user

def main():
    # Test the system
    register("testuser", "weak", "<EMAIL>")
    login("testuser", "weak")

if __name__ == "__main__":
    main()"""
    }
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,
        diff_format=True
    )
    
    print("Problem:", problem.strip())
    print(f"\nFiles to process: {list(file_contents.keys())}")
    
    if result['success']:
        print(f"\nEdited files: {result['edited_files']}")
        print("\nGenerated multi-file patch:")
        print(result['model_patch'])
        
        # Save patch to file
        with open("anthropic_auth_fix.patch", "w") as f:
            f.write(result['model_patch'])
        print("\nPatch saved to: anthropic_auth_fix.patch")
    else:
        print(f"Error: {result['error']}")


def example_deepseek_multifile_fix():
    """Example using DeepSeek API for multi-file bug fixing"""
    print("\n" + "="*70)
    print("EXAMPLE: DeepSeek Multi-file Bug Fix")
    print("="*70)
    
    # Initialize generator with DeepSeek
    generator = AgentlessMultifilePatchGenerator(
        model_name="deepseek-coder",
        llm_provider="deepseek",
        api_key=os.getenv("DEEPSEEK_API_KEY"),  # Set your API key
        temperature=0.0,
        max_tokens=4096
    )
    
    problem = """
    The web API has several issues:
    1. No input validation for JSON requests
    2. Database connections are not properly managed
    3. Error responses don't follow REST standards
    Fix these issues to make the API more robust.
    """
    
    file_contents = {
        "api.py": """from flask import Flask, request, jsonify
import sqlite3

app = Flask(__name__)

@app.route('/api/users', methods=['POST'])
def create_user():
    data = request.get_json()  # Bug: no validation
    
    username = data['username']  # Bug: no key check
    email = data['email']        # Bug: no validation
    
    # Bug: no connection management
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    cursor.execute("INSERT INTO users (username, email) VALUES (?, ?)", 
                   (username, email))
    conn.commit()
    # Bug: connection not closed properly
    
    return jsonify({'message': 'User created'})

@app.route('/api/users/<username>', methods=['GET'])
def get_user(username):
    # Bug: no connection management
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
    user = cursor.fetchone()
    # Bug: connection not closed
    
    if user:
        return jsonify({'username': user[0], 'email': user[1]})
    else:
        return "User not found", 404  # Bug: not proper JSON response""",
        
        "database.py": """import sqlite3

def get_connection():
    # Bug: creates new connection every time
    return sqlite3.connect('users.db')

def init_database():
    conn = get_connection()
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            username TEXT PRIMARY KEY,
            email TEXT NOT NULL
        )
    ''')
    conn.commit()
    # Bug: connection not closed
    
def close_connection(conn):
    # Bug: no error handling
    conn.close()""",
        
        "validators.py": """import re

def validate_email(email):
    # Bug: very basic validation
    return '@' in email

def validate_username(username):
    # Bug: no length or character validation
    return len(username) > 0

def validate_json_data(data, required_fields):
    # Bug: not implemented
    return True"""
    }
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,
        diff_format=True
    )
    
    print("Problem:", problem.strip())
    print(f"\nFiles to process: {list(file_contents.keys())}")
    
    if result['success']:
        print(f"\nEdited files: {result['edited_files']}")
        print("\nGenerated multi-file patch:")
        print(result['model_patch'])
        
        # Save patch to file
        with open("deepseek_api_fix.patch", "w") as f:
            f.write(result['model_patch'])
        print("\nPatch saved to: deepseek_api_fix.patch")
    else:
        print(f"Error: {result['error']}")


def setup_instructions():
    """Print setup instructions for using real LLM APIs"""
    print("="*70)
    print("SETUP INSTRUCTIONS FOR REAL LLM USAGE")
    print("="*70)
    
    print("\n1. Install required packages:")
    print("   pip install openai anthropic")
    
    print("\n2. Set up API keys as environment variables:")
    print("   export OPENAI_API_KEY='your-openai-api-key'")
    print("   export ANTHROPIC_API_KEY='your-anthropic-api-key'")
    print("   export DEEPSEEK_API_KEY='your-deepseek-api-key'")
    
    print("\n3. Or pass API keys directly to the generator:")
    print("   generator = AgentlessMultifilePatchGenerator(")
    print("       model_name='gpt-4',")
    print("       llm_provider='openai',")
    print("       api_key='your-api-key'")
    print("   )")
    
    print("\n4. Supported providers and models:")
    print("   - OpenAI: gpt-4, gpt-3.5-turbo, gpt-4-turbo")
    print("   - Anthropic: claude-3-sonnet-20240229, claude-3-opus-20240229")
    print("   - DeepSeek: deepseek-coder, deepseek-chat")
    print("   - Custom: Any OpenAI-compatible API")
    
    print("\n5. Command line usage:")
    print("   python agentless_multifile_patch.py \\")
    print("       --problem 'Fix the bugs' \\")
    print("       --files file1.py file2.py \\")
    print("       --provider openai \\")
    print("       --model gpt-4 \\")
    print("       --api-key $OPENAI_API_KEY \\")
    print("       --diff-format")


def main():
    """Run examples based on available API keys"""
    print("Agentless Multi-file Patch Generator - Real LLM Examples")
    print("=" * 70)
    
    setup_instructions()
    
    # Check which APIs are available
    has_openai = bool(os.getenv("OPENAI_API_KEY"))
    has_anthropic = bool(os.getenv("ANTHROPIC_API_KEY"))
    has_deepseek = bool(os.getenv("DEEPSEEK_API_KEY"))
    
    print(f"\nAPI Key Status:")
    print(f"OpenAI: {'✓' if has_openai else '✗'}")
    print(f"Anthropic: {'✓' if has_anthropic else '✗'}")
    print(f"DeepSeek: {'✓' if has_deepseek else '✗'}")
    
    if not any([has_openai, has_anthropic, has_deepseek]):
        print("\nNo API keys found. Please set up at least one API key to run examples.")
        return
    
    # Run available examples
    if has_openai:
        example_openai_multifile_fix()
    
    if has_anthropic:
        example_anthropic_multifile_fix()
    
    if has_deepseek:
        example_deepseek_multifile_fix()
    
    print("\n" + "="*70)
    print("EXAMPLES COMPLETED")
    print("="*70)


if __name__ == "__main__":
    main()
