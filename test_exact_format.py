#!/usr/bin/env python3
"""
Test script to verify the exact Git diff format matches the expected output.
"""

from agentless_multifile_patch import AgentlessMultifilePatchGenerator

def test_exact_format():
    """Test the exact Git diff format generation."""
    
    # Create generator without LLM (will use simulation)
    generator = AgentlessMultifilePatchGenerator()
    
    # Test case: astropy separable.py fix - exact content from the issue
    original_content = """def _cstack(left, right):
    \"\"\"
    Function corresponding to the stack operation.
    \"\"\"
    noutp = left.shape[0] + right.shape[0]
    cleft = _coord_matrix(left, 'left', noutp)
    if right.shape[0] == noutp:
        cright = _coord_matrix(right, 'right', noutp)
    else:
        cright = np.zeros((noutp, right.shape[1]))
        cright[-right.shape[0]:, -right.shape[1]:] = 1

    return np.hstack([cleft, cright])"""

    new_content = """def _cstack(left, right):
    \"\"\"
    Function corresponding to the stack operation.
    \"\"\"
    noutp = left.shape[0] + right.shape[0]
    cleft = _coord_matrix(left, 'left', noutp)
    if right.shape[0] == noutp:
        cright = _coord_matrix(right, 'right', noutp)
    else:
        cright = np.zeros((noutp, right.shape[1]))
        cright[-right.shape[0]:, -right.shape[1]:] = right

    return np.hstack([cleft, cright])"""

    # Test file changes
    file_changes = {
        "separable.py": (original_content, new_content)
    }
    
    # Generate diff with module prefix
    diff = generator.create_git_diff(file_changes, module_prefix="astropy/modeling")
    
    print("Generated Git diff:")
    print("=" * 60)
    print(diff)
    print("=" * 60)
    
    # Expected format (what you want)
    expected_diff = """diff --git a/astropy/modeling/separable.py b/astropy/modeling/separable.py
--- a/astropy/modeling/separable.py
+++ b/astropy/modeling/separable.py
@@ -242,7 +242,7 @@ def _cstack(left, right):
         cright = _coord_matrix(right, 'right', noutp)
     else:
         cright = np.zeros((noutp, right.shape[1]))
-        cright[-right.shape[0]:, -right.shape[1]:] = 1
+        cright[-right.shape[0]:, -right.shape[1]:] = right
 
     return np.hstack([cleft, cright])"""
    
    print("\nExpected format:")
    print("=" * 60)
    print(expected_diff)
    print("=" * 60)
    
    # Check key differences
    lines = diff.split('\n')
    print("\nAnalyzing differences:")
    print(f"- Generated has {len(lines)} lines")
    print(f"- Contains 'index' line: {'index' in diff}")
    print(f"- Contains correct change: {'-        cright[-right.shape[0]:, -right.shape[1]:] = 1' in diff}")
    print(f"- Contains correct replacement: {'+        cright[-right.shape[0]:, -right.shape[1]:] = right' in diff}")

if __name__ == "__main__":
    test_exact_format()
