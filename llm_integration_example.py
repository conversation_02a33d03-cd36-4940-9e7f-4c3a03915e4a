#!/usr/bin/env python3
"""
LLM Integration Examples for chunk_to_patch.py

This file shows how to integrate real LLM APIs with the ChunkToPatchGenerator.
Replace the _simulate_llm_call method with one of these implementations.
"""

import json
import os
from typing import Dict, Any


class OpenAIIntegration:
    """OpenAI API integration example."""
    
    def __init__(self, api_key: str = None, model: str = "gpt-4"):
        """
        Initialize OpenAI integration.
        
        Args:
            api_key: OpenAI API key (if None, reads from environment)
            model: Model name to use
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.model = model
        
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
    
    def call_llm(self, message: str, temperature: float = 0.0) -> str:
        """
        Call OpenAI API.
        
        Args:
            message: The prompt message
            temperature: Generation temperature
            
        Returns:
            LLM response text
        """
        try:
            import openai
            
            client = openai.OpenAI(api_key=self.api_key)
            
            response = client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": message}
                ],
                temperature=temperature,
                max_tokens=2048
            )
            
            return response.choices[0].message.content
            
        except ImportError:
            raise ImportError("openai package is required. Install with: pip install openai")
        except Exception as e:
            raise Exception(f"OpenAI API call failed: {e}")


class AnthropicIntegration:
    """Anthropic Claude API integration example."""
    
    def __init__(self, api_key: str = None, model: str = "claude-3-sonnet-20240229"):
        """
        Initialize Anthropic integration.
        
        Args:
            api_key: Anthropic API key (if None, reads from environment)
            model: Model name to use
        """
        self.api_key = api_key or os.getenv('ANTHROPIC_API_KEY')
        self.model = model
        
        if not self.api_key:
            raise ValueError("Anthropic API key is required")
    
    def call_llm(self, message: str, temperature: float = 0.0) -> str:
        """
        Call Anthropic API.
        
        Args:
            message: The prompt message
            temperature: Generation temperature
            
        Returns:
            LLM response text
        """
        try:
            import anthropic
            
            client = anthropic.Anthropic(api_key=self.api_key)
            
            response = client.messages.create(
                model=self.model,
                max_tokens=2048,
                temperature=temperature,
                messages=[
                    {"role": "user", "content": message}
                ]
            )
            
            return response.content[0].text
            
        except ImportError:
            raise ImportError("anthropic package is required. Install with: pip install anthropic")
        except Exception as e:
            raise Exception(f"Anthropic API call failed: {e}")


class LocalLLMIntegration:
    """Local LLM integration example (using ollama or similar)."""
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "codellama"):
        """
        Initialize local LLM integration.
        
        Args:
            base_url: Base URL for the local LLM API
            model: Model name to use
        """
        self.base_url = base_url
        self.model = model
    
    def call_llm(self, message: str, temperature: float = 0.0) -> str:
        """
        Call local LLM API.
        
        Args:
            message: The prompt message
            temperature: Generation temperature
            
        Returns:
            LLM response text
        """
        try:
            import requests
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": self.model,
                    "prompt": message,
                    "stream": False,
                    "options": {
                        "temperature": temperature
                    }
                }
            )
            
            response.raise_for_status()
            return response.json()["response"]
            
        except ImportError:
            raise ImportError("requests package is required. Install with: pip install requests")
        except Exception as e:
            raise Exception(f"Local LLM API call failed: {e}")


# Modified ChunkToPatchGenerator with real LLM integration
from chunk_to_patch import ChunkToPatchGenerator

class EnhancedChunkToPatchGenerator(ChunkToPatchGenerator):
    """Enhanced version with real LLM integration."""
    
    def __init__(self, llm_integration, model_name: str = "gpt-4", temperature: float = 0.0):
        """
        Initialize with LLM integration.
        
        Args:
            llm_integration: LLM integration instance (OpenAI, Anthropic, etc.)
            model_name: Model name for compatibility
            temperature: Generation temperature
        """
        super().__init__(model_name, temperature)
        self.llm_integration = llm_integration
    
    def _simulate_llm_call(self, message: str) -> str:
        """
        Replace simulation with real LLM call.
        
        Args:
            message: The prompt message
            
        Returns:
            LLM response text
        """
        return self.llm_integration.call_llm(message, self.temperature)


# Usage examples
def example_openai_usage():
    """Example using OpenAI API."""
    print("Example: OpenAI Integration")
    print("-" * 40)
    
    try:
        # Initialize OpenAI integration
        openai_integration = OpenAIIntegration(model="gpt-4")
        
        # Create enhanced generator
        generator = EnhancedChunkToPatchGenerator(
            llm_integration=openai_integration,
            temperature=0.0
        )
        
        # Example usage
        problem = "The function should return a greeting but returns None"
        code_chunk = "def greet():\n    return None"
        file_path = "greetings.py"
        
        result = generator.generate_patch(
            problem_statement=problem,
            code_chunk=code_chunk,
            file_path=file_path
        )
        
        print("Result:", json.dumps(result, indent=2))
        
    except Exception as e:
        print(f"Error: {e}")


def example_anthropic_usage():
    """Example using Anthropic API."""
    print("\nExample: Anthropic Integration")
    print("-" * 40)
    
    try:
        # Initialize Anthropic integration
        anthropic_integration = AnthropicIntegration()
        
        # Create enhanced generator
        generator = EnhancedChunkToPatchGenerator(
            llm_integration=anthropic_integration,
            temperature=0.0
        )
        
        # Example usage
        problem = "Fix the division by zero error"
        code_chunk = "def divide(a, b):\n    return a / b"
        file_path = "math_utils.py"
        
        result = generator.generate_patch(
            problem_statement=problem,
            code_chunk=code_chunk,
            file_path=file_path
        )
        
        print("Result:", json.dumps(result, indent=2))
        
    except Exception as e:
        print(f"Error: {e}")


def example_local_llm_usage():
    """Example using local LLM."""
    print("\nExample: Local LLM Integration")
    print("-" * 40)
    
    try:
        # Initialize local LLM integration
        local_integration = LocalLLMIntegration(
            base_url="http://localhost:11434",
            model="codellama"
        )
        
        # Create enhanced generator
        generator = EnhancedChunkToPatchGenerator(
            llm_integration=local_integration,
            temperature=0.1
        )
        
        # Example usage
        problem = "Add error handling to the function"
        code_chunk = "def process_data(data):\n    return data.upper()"
        file_path = "processor.py"
        
        result = generator.generate_patch(
            problem_statement=problem,
            code_chunk=code_chunk,
            file_path=file_path
        )
        
        print("Result:", json.dumps(result, indent=2))
        
    except Exception as e:
        print(f"Error: {e}")


def setup_instructions():
    """Print setup instructions for different LLM providers."""
    print("Setup Instructions:")
    print("=" * 50)
    
    print("\n1. OpenAI Setup:")
    print("   pip install openai")
    print("   export OPENAI_API_KEY='your-api-key'")
    
    print("\n2. Anthropic Setup:")
    print("   pip install anthropic")
    print("   export ANTHROPIC_API_KEY='your-api-key'")
    
    print("\n3. Local LLM Setup (Ollama):")
    print("   # Install Ollama: https://ollama.ai/")
    print("   ollama pull codellama")
    print("   ollama serve")
    print("   pip install requests")
    
    print("\n4. Usage:")
    print("   python llm_integration_example.py")


if __name__ == "__main__":
    setup_instructions()
    
    # Uncomment the integration you want to test
    # example_openai_usage()
    # example_anthropic_usage()
    # example_local_llm_usage()
