#!/usr/bin/env python3
"""
Test Improved Diff Format

This script tests the improved diff format with proper path handling using os.path.join
and module prefixes, plus returning the LLM message.
"""

import os
from agentless_multifile_patch import AgentlessMultifilePatchGenerator


def test_module_prefix_diff():
    """Test diff generation with module prefix"""
    print("="*70)
    print("TEST: Module Prefix Diff Format")
    print("="*70)
    
    # Your API configuration
    api_key = "1830498799983480864"
    base_url = "https://aigc.sankuai.com/v1/openai/native"
    
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",
        llm_provider="custom",
        api_key=api_key,
        base_url=base_url,
        temperature=0.0,
        max_tokens=2048
    )
    
    problem = "Fix the coordinate matrix calculation bug in the _cstack function"
    
    # Simulate astropy-like file structure
    file_contents = {
        "separable.py": """import numpy as np

def _coord_matrix(model, pos, n_outputs):
    '''Create coordinate matrix for model'''
    if pos == 'left':
        return np.eye(n_outputs, model.shape[1])
    else:
        return np.zeros((n_outputs, model.shape[1]))

def _cstack(left, right):
    '''Stack coordinate matrices'''
    noutp = left.shape[0] + right.shape[0]
    
    if left.ndim == 1:
        cleft = _coord_matrix(left, 'left', noutp)
    else:
        cleft = np.zeros((noutp, left.shape[1]))
        cleft[:left.shape[0], :left.shape[1]] = left
    
    if right.ndim == 1:
        cright = _coord_matrix(right, 'right', noutp)
    else:
        cright = np.zeros((noutp, right.shape[1]))
        cright[-right.shape[0]:, -right.shape[1]:] = 1  # Bug: should be 'right'
    
    return np.hstack([cleft, cright])"""
    }
    
    # Specify the problematic line
    file_locations = {
        "separable.py": [(24, 24)]  # The line with the bug
    }
    
    print("Problem:", problem)
    print(f"Files: {list(file_contents.keys())}")
    print(f"Module prefix: astropy/modeling")
    
    try:
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            file_locations=file_locations,
            context_window=3,
            diff_format=True,
            module_prefix="astropy/modeling"  # Test module prefix
        )
        
        if result['success']:
            print(f"✅ Success!")
            print(f"Edited files: {result['edited_files']}")
            
            print(f"\n📋 Generated diff with module prefix:")
            print(result['model_patch'])
            
            print(f"\n📨 LLM Message (first 300 chars):")
            if 'llm_message' in result:
                message = result['llm_message']
                print(message[:300] + "..." if len(message) > 300 else message)
            else:
                print("LLM message not available")
            
            # Verify the diff format
            patch_lines = result['model_patch'].split('\n')
            for line in patch_lines:
                if line.startswith('diff --git'):
                    print(f"\n🔍 Diff header: {line}")
                    if 'astropy/modeling/separable.py' in line:
                        print("✅ Module prefix correctly applied!")
                    else:
                        print("❌ Module prefix missing!")
                elif line.startswith('---') or line.startswith('+++'):
                    print(f"🔍 File path: {line}")
            
            return True
        else:
            print(f"❌ Failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def test_no_module_prefix():
    """Test diff generation without module prefix"""
    print("\n" + "="*70)
    print("TEST: No Module Prefix (Default)")
    print("="*70)
    
    api_key = "1830498799983480864"
    base_url = "https://aigc.sankuai.com/v1/openai/native"
    
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",
        llm_provider="custom",
        api_key=api_key,
        base_url=base_url,
        temperature=0.0,
        max_tokens=1024
    )
    
    problem = "Add input validation to the function"
    
    file_contents = {
        "utils.py": """def process_data(data):
    # No input validation
    return data.upper()

def validate_input(value):
    return value is not None"""
    }
    
    file_locations = {
        "utils.py": [(2, 3)]  # process_data function
    }
    
    print("Problem:", problem)
    print(f"Files: {list(file_contents.keys())}")
    print(f"Module prefix: None (default)")
    
    try:
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            file_locations=file_locations,
            context_window=2,
            diff_format=True
            # No module_prefix parameter - should use default
        )
        
        if result['success']:
            print(f"✅ Success!")
            print(f"Edited files: {result['edited_files']}")
            
            print(f"\n📋 Generated diff without module prefix:")
            print(result['model_patch'])
            
            print(f"\n📨 LLM Message (first 200 chars):")
            if 'llm_message' in result:
                message = result['llm_message']
                print(message[:200] + "..." if len(message) > 200 else message)
            
            # Verify the diff format
            patch_lines = result['model_patch'].split('\n')
            for line in patch_lines:
                if line.startswith('diff --git'):
                    print(f"\n🔍 Diff header: {line}")
                    if line == 'diff --git a/utils.py b/utils.py':
                        print("✅ Default path format correct!")
                    else:
                        print(f"⚠️  Unexpected format: {line}")
            
            return True
        else:
            print(f"❌ Failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def test_nested_module_paths():
    """Test with nested module paths"""
    print("\n" + "="*70)
    print("TEST: Nested Module Paths")
    print("="*70)
    
    api_key = "1830498799983480864"
    base_url = "https://aigc.sankuai.com/v1/openai/native"
    
    generator = AgentlessMultifilePatchGenerator(
        model_name="anthropic.claude-sonnet-4",
        llm_provider="custom",
        api_key=api_key,
        base_url=base_url,
        temperature=0.0,
        max_tokens=1024
    )
    
    problem = "Fix the import statement and add error handling"
    
    file_contents = {
        "core/base.py": """from .utils import helper

class BaseClass:
    def __init__(self):
        self.data = helper()  # May fail if helper() raises exception""",
        
        "core/utils.py": """def helper():
    # Missing error handling
    return process_data()

def process_data():
    return "processed" """
    }
    
    file_locations = {
        "core/base.py": [(4, 5)],
        "core/utils.py": [(2, 3)]
    }
    
    print("Problem:", problem)
    print(f"Files: {list(file_contents.keys())}")
    print(f"Module prefix: myproject/src")
    
    try:
        result = generator.generate_multifile_patch(
            problem_statement=problem,
            file_contents=file_contents,
            file_locations=file_locations,
            context_window=2,
            diff_format=True,
            module_prefix="myproject/src"  # Nested module prefix
        )
        
        if result['success']:
            print(f"✅ Success!")
            print(f"Edited files: {result['edited_files']}")
            
            print(f"\n📋 Generated diff with nested module prefix:")
            print(result['model_patch'])
            
            # Verify nested paths
            patch_lines = result['model_patch'].split('\n')
            for line in patch_lines:
                if line.startswith('diff --git'):
                    print(f"\n🔍 Diff header: {line}")
                    if 'myproject/src/core/' in line:
                        print("✅ Nested module prefix correctly applied!")
                    else:
                        print(f"❌ Nested module prefix issue: {line}")
            
            return True
        else:
            print(f"❌ Failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def main():
    """Run all improved diff format tests"""
    print("Improved Diff Format Test Suite")
    print("=" * 70)
    
    print("Testing improvements:")
    print("  ✅ Proper path handling with os.path.join")
    print("  ✅ Module prefix support (e.g., astropy/modeling)")
    print("  ✅ LLM message returned in results")
    print("  ✅ Cross-platform path compatibility")
    
    # Run tests
    results = []
    
    # Test 1: Module prefix
    results.append(("Module Prefix Diff", test_module_prefix_diff()))
    
    # Test 2: No module prefix
    results.append(("No Module Prefix", test_no_module_prefix()))
    
    # Test 3: Nested module paths
    results.append(("Nested Module Paths", test_nested_module_paths()))
    
    # Summary
    print("\n" + "="*70)
    print("IMPROVED DIFF FORMAT TEST SUMMARY")
    print("="*70)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All improvements working correctly!")
        print("✅ Path handling with os.path.join: Working")
        print("✅ Module prefix support: Working")
        print("✅ LLM message return: Working")
        print("✅ Cross-platform compatibility: Working")
    else:
        print(f"\n⚠️  {total - passed} tests failed.")
    
    print(f"\n📋 Key Improvements:")
    print(f"  🔧 os.path.join for proper path construction")
    print(f"  📁 module_prefix parameter for organized diffs")
    print(f"  📨 llm_message returned in results")
    print(f"  🌐 Cross-platform path separator handling")


if __name__ == "__main__":
    main()
