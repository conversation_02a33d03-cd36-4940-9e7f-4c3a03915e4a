#!/usr/bin/env python3
"""
Example usage of chunk_to_patch.py

This script demonstrates practical usage of the ChunkToPatchGenerator
with realistic examples.
"""

from chunk_to_patch import ChunkToPatchGenerator
import json


def example_1_simple_bug_fix():
    """Example 1: Simple bug fix - function returning None instead of value."""
    print("="*60)
    print("EXAMPLE 1: Simple Bug Fix")
    print("="*60)
    
    generator = ChunkToPatchGenerator()
    
    problem = """
    The greet() function should return a greeting message but currently returns None.
    It should return "Hello, World!" instead.
    """
    
    code_chunk = """def greet():
    # TODO: implement greeting
    return None"""
    
    file_path = "greetings.py"
    
    result = generator.generate_patch(
        problem_statement=problem,
        code_chunk=code_chunk,
        file_path=file_path
    )
    
    print("Problem:", problem.strip())
    print("\nCode Chunk:")
    print(code_chunk)
    print("\nGenerated Patch:")
    if result['success']:
        print(result['model_patch'])
    else:
        print(f"Error: {result['error']}")


def example_2_math_function_fix():
    """Example 2: Math function fix with full file context."""
    print("\n" + "="*60)
    print("EXAMPLE 2: Math Function Fix")
    print("="*60)
    
    generator = ChunkToPatchGenerator()
    
    problem = """
    The calculate_sum function has a bug where it doesn't handle negative numbers correctly.
    It should return the absolute sum of all numbers (sum of absolute values).
    """
    
    code_chunk = """def calculate_sum(numbers):
    total = 0
    for num in numbers:
        total += num
    return total"""
    
    original_content = """# Math utilities module

def calculate_sum(numbers):
    total = 0
    for num in numbers:
        total += num
    return total

def calculate_average(numbers):
    if not numbers:
        return 0
    return calculate_sum(numbers) / len(numbers)

def main():
    test_numbers = [1, -2, 3, -4, 5]
    print(f"Sum: {calculate_sum(test_numbers)}")
    print(f"Average: {calculate_average(test_numbers)}")

if __name__ == "__main__":
    main()"""
    
    file_path = "math_utils.py"
    
    result = generator.generate_patch(
        problem_statement=problem,
        code_chunk=code_chunk,
        file_path=file_path,
        original_content=original_content
    )
    
    print("Problem:", problem.strip())
    print("\nCode Chunk:")
    print(code_chunk)
    print("\nGenerated Patch:")
    if result['success']:
        print(result['model_patch'])
        print("\nOriginal function:")
        print("def calculate_sum(numbers):")
        print("    total = 0")
        print("    for num in numbers:")
        print("        total += num")
        print("    return total")
        print("\nFixed function:")
        print("def calculate_sum(numbers):")
        print("    total = 0")
        print("    for num in numbers:")
        print("        total += abs(num)  # Use absolute value")
        print("    return total")
    else:
        print(f"Error: {result['error']}")


def example_3_error_handling():
    """Example 3: Adding error handling to a function."""
    print("\n" + "="*60)
    print("EXAMPLE 3: Error Handling")
    print("="*60)
    
    generator = ChunkToPatchGenerator()
    
    problem = """
    The divide function needs error handling to prevent division by zero.
    It should raise a ValueError with message "Cannot divide by zero" when b is 0.
    """
    
    code_chunk = """def divide(a, b):
    return a / b"""
    
    file_path = "calculator.py"
    
    result = generator.generate_patch(
        problem_statement=problem,
        code_chunk=code_chunk,
        file_path=file_path
    )
    
    print("Problem:", problem.strip())
    print("\nCode Chunk:")
    print(code_chunk)
    print("\nGenerated Patch:")
    if result['success']:
        print(result['model_patch'])
    else:
        print(f"Error: {result['error']}")


def example_4_with_file_output():
    """Example 4: Generate patch and save to file."""
    print("\n" + "="*60)
    print("EXAMPLE 4: Save Patch to File")
    print("="*60)
    
    generator = ChunkToPatchGenerator()
    
    problem = """
    The process_data function should handle None input gracefully.
    It should return an empty string when data is None.
    """
    
    code_chunk = """def process_data(data):
    return data.upper()"""
    
    file_path = "processor.py"
    
    result = generator.generate_patch(
        problem_statement=problem,
        code_chunk=code_chunk,
        file_path=file_path
    )
    
    print("Problem:", problem.strip())
    print("\nCode Chunk:")
    print(code_chunk)
    
    if result['success']:
        # Save patch to file
        patch_filename = "example_patch.diff"
        with open(patch_filename, 'w') as f:
            f.write(result['model_patch'])
        
        print(f"\nPatch saved to: {patch_filename}")
        print("\nPatch content:")
        print(result['model_patch'])
        
        print(f"\nTo apply this patch, run:")
        print(f"git apply {patch_filename}")
    else:
        print(f"Error: {result['error']}")


def show_usage_summary():
    """Show usage summary."""
    print("\n" + "="*60)
    print("USAGE SUMMARY")
    print("="*60)
    
    print("\n1. Basic Python API Usage:")
    print("""
from chunk_to_patch import ChunkToPatchGenerator

generator = ChunkToPatchGenerator()
result = generator.generate_patch(
    problem_statement="Description of the issue",
    code_chunk="problematic code",
    file_path="path/to/file.py"
)

if result['success']:
    print(result['model_patch'])
""")
    
    print("\n2. Command Line Usage:")
    print("""
python3 chunk_to_patch.py \\
    --problem "Issue description" \\
    --chunk "code chunk" \\
    --file "file.py" \\
    --output "patch.diff"
""")
    
    print("\n3. With Original File Content:")
    print("""
result = generator.generate_patch(
    problem_statement="Issue description",
    code_chunk="problematic code chunk",
    file_path="file.py",
    original_content="full file content"
)
""")


def main():
    """Run all examples."""
    print("Chunk to Patch Generator - Usage Examples")
    print("=" * 60)
    
    example_1_simple_bug_fix()
    example_2_math_function_fix()
    example_3_error_handling()
    example_4_with_file_output()
    show_usage_summary()
    
    print("\n" + "="*60)
    print("ALL EXAMPLES COMPLETED")
    print("="*60)


if __name__ == "__main__":
    main()
