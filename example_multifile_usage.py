#!/usr/bin/env python3
"""
Example usage of agentless_multifile_patch.py

This script demonstrates practical usage of the AgentlessMultifilePatchGenerator
with realistic multi-file scenarios that mimic real-world development issues.
"""

from agentless_multifile_patch import AgentlessMultifilePatchGenerator
import json
import os


def example_1_calculator_bug_fix():
    """Example 1: Multi-file bug fix - calculator with exception handling."""
    print("="*70)
    print("EXAMPLE 1: Calculator Bug Fix (Multi-file)")
    print("="*70)
    
    generator = AgentlessMultifilePatchGenerator()
    
    problem = """
    There's a critical bug in the calculator module where division by zero is not handled properly.
    The main.py file calls the calculator functions but doesn't handle the exceptions that can be raised.
    Both files need to be fixed:
    1. calculator.py should raise appropriate exceptions for invalid operations
    2. main.py should handle these exceptions gracefully
    """
    
    file_contents = {
        "calculator.py": """def add(a, b):
    return a + b

def subtract(a, b):
    return a - b

def multiply(a, b):
    return a * b

def divide(a, b):
    return a / b  # Bug: no zero check

def power(a, b):
    return a ** b""",
        
        "main.py": """from calculator import add, subtract, multiply, divide, power

def main():
    a, b = 10, 0
    
    print("Calculator Results:")
    print(f"Addition: {add(a, b)}")
    print(f"Subtraction: {subtract(a, b)}")
    print(f"Multiplication: {multiply(a, b)}")
    print(f"Division: {divide(a, b)}")  # Bug: will crash here
    print(f"Power: {power(a, b)}")

if __name__ == "__main__":
    main()"""
    }
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,
        diff_format=True  # Use SEARCH/REPLACE format
    )
    
    print("Problem:", problem.strip())
    print(f"\nFiles to process: {list(file_contents.keys())}")
    
    if result['success']:
        print(f"\nEdited files: {result['edited_files']}")
        print("\nGenerated multi-file patch:")
        print(result['model_patch'])
        
        # Save patch to file
        with open("calculator_fix.patch", "w") as f:
            f.write(result['model_patch'])
        print("\nPatch saved to: calculator_fix.patch")
    else:
        print(f"Error: {result['error']}")


def example_2_logging_configuration():
    """Example 2: Logging configuration across multiple modules."""
    print("\n" + "="*70)
    print("EXAMPLE 2: Logging Configuration Fix")
    print("="*70)
    
    generator = AgentlessMultifilePatchGenerator()
    
    problem = """
    The logging configuration is inconsistent across the application.
    The config.py has the wrong log level set to ERROR, which prevents INFO messages from showing.
    The utils.py and main.py files are trying to log INFO messages that won't appear.
    All three files need to be updated to have consistent and proper logging.
    """
    
    file_contents = {
        "config.py": """import logging
import os

# Bug: wrong log level - too restrictive
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def get_config():
    return {
        "debug": True,
        "log_file": "app.log"
    }""",
        
        "utils.py": """import logging
from config import get_config

def process_data(data):
    logging.info("Starting data processing")  # Won't show due to ERROR level
    
    if not data:
        logging.warning("Empty data received")
        return []
    
    result = [item.upper() for item in data]
    logging.info(f"Processed {len(result)} items")  # Won't show
    return result

def validate_input(input_data):
    logging.info("Validating input data")  # Won't show
    return isinstance(input_data, list)""",
        
        "main.py": """import logging
from config import get_config
from utils import process_data, validate_input

def main():
    config = get_config()
    
    logging.info("Application starting")  # Won't show due to ERROR level
    
    test_data = ["hello", "world", "test"]
    
    if validate_input(test_data):
        result = process_data(test_data)
        print(f"Result: {result}")
        logging.info("Application completed successfully")  # Won't show
    else:
        logging.error("Invalid input data")
        
if __name__ == "__main__":
    main()"""
    }
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,
        diff_format=True
    )
    
    print("Problem:", problem.strip())
    print(f"\nFiles to process: {list(file_contents.keys())}")
    
    if result['success']:
        print(f"\nEdited files: {result['edited_files']}")
        print("\nGenerated multi-file patch:")
        print(result['model_patch'])
    else:
        print(f"Error: {result['error']}")


def example_3_with_specific_locations():
    """Example 3: Using specific file locations for targeted fixes."""
    print("\n" + "="*70)
    print("EXAMPLE 3: Targeted Fixes with File Locations")
    print("="*70)
    
    generator = AgentlessMultifilePatchGenerator()
    
    problem = """
    There are specific bugs in the authentication system:
    1. Line 8 in auth.py: password validation is too weak
    2. Line 12 in user.py: user creation doesn't hash passwords
    3. Line 6 in main.py: login attempt doesn't handle auth failures
    """
    
    file_contents = {
        "auth.py": """import hashlib

def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

def validate_password(password):
    # Bug: too weak validation
    return len(password) > 3  # Line 8 - needs stronger validation

def authenticate(username, password):
    # Simplified auth logic
    hashed = hash_password(password)
    return check_user_credentials(username, hashed)

def check_user_credentials(username, hashed_password):
    # Mock implementation
    return True""",
        
        "user.py": """from auth import hash_password

class User:
    def __init__(self, username, password):
        self.username = username
        self.password = password  # Bug: storing plain password
        
    def create_user(self, username, password):
        # Bug: not hashing password before storage
        user = User(username, password)  # Line 12 - should hash password
        return user
        
    def get_username(self):
        return self.username""",
        
        "main.py": """from auth import authenticate
from user import User

def login(username, password):
    # Bug: not handling authentication failures properly
    result = authenticate(username, password)  # Line 6 - needs error handling
    print("Login successful")
    return result

def main():
    username = "testuser"
    password = "weak"
    
    login(username, password)

if __name__ == "__main__":
    main()"""
    }
    
    # Specify exact locations of the bugs
    file_locations = {
        "auth.py": [(8, 8)],    # Password validation line
        "user.py": [(12, 12)],  # User creation line
        "main.py": [(6, 6)]     # Authentication call line
    }
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,
        file_locations=file_locations,
        context_window=3  # Small context window for focused fixes
    )
    
    print("Problem:", problem.strip())
    print(f"\nFiles with specific locations: {list(file_locations.keys())}")
    print(f"Target locations: {file_locations}")
    
    if result['success']:
        print(f"\nEdited files: {result['edited_files']}")
        print("\nGenerated targeted patch:")
        print(result['model_patch'])
    else:
        print(f"Error: {result['error']}")


def example_4_context_management():
    """Example 4: Demonstrate context length management."""
    print("\n" + "="*70)
    print("EXAMPLE 4: Context Length Management")
    print("="*70)
    
    # Use small context length to trigger management
    generator = AgentlessMultifilePatchGenerator(max_context_length=2000)
    
    problem = "Fix import statements and add proper error handling in all modules"
    
    # Create multiple large files to test context management
    file_contents = {}
    for i in range(6):  # Create 6 files
        file_contents[f"module_{i}.py"] = f"""# Module {i} - Large file for context testing
import os
import sys
import json
import re
import subprocess
import tempfile
import uuid
from collections import OrderedDict
from difflib import unified_diff
from typing import Dict, List, Tuple, Optional, Any

class Module{i}Class:
    '''Large class with many methods to increase file size.'''
    
    def __init__(self):
        self.data = {{f"key_{{j}}": f"value_{{j}}" for j in range(50)}}
        self.config = self._load_config()
        self.logger = self._setup_logging()
    
    def _load_config(self):
        '''Load configuration from file.'''
        config_data = {{}}
        for k in range(20):
            config_data[f"setting_{k}"] = f"value_{k}"
        return config_data
    
    def _setup_logging(self):
        '''Setup logging configuration.'''
        import logging
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)
    
    def process_data(self, input_data):
        '''Process input data with error handling.'''
        try:
            result = []
            for item in input_data:
                processed = self._process_item(item)
                result.append(processed)
            return result
        except Exception as e:
            self.logger.error(f"Error processing data: {{e}}")
            return []
    
    def _process_item(self, item):
        '''Process individual item.'''
        if isinstance(item, str):
            return item.upper()
        elif isinstance(item, (int, float)):
            return item * 2
        else:
            return str(item)
    
    def save_results(self, results, filename):
        '''Save results to file.'''
        try:
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2)
            self.logger.info(f"Results saved to {{filename}}")
        except IOError as e:
            self.logger.error(f"Failed to save results: {{e}}")
    
    def run(self):
        '''Main execution method.'''
        test_data = [f"item_{{i}}" for i in range(10)]
        results = self.process_data(test_data)
        self.save_results(results, f"output_module_{i}.json")
        return results

def main():
    '''Main function for module {i}.'''
    module = Module{i}Class()
    results = module.run()
    print(f"Module {i} processed {{len(results)}} items")

if __name__ == "__main__":
    main()
"""
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents
    )
    
    print("Problem:", problem.strip())
    print(f"\nOriginal files: {len(file_contents)}")
    print(f"File sizes: {[len(content) for content in file_contents.values()]}")
    
    if result['success']:
        print(f"\nProcessed files: {len(result['edited_files'])}")
        print(f"Edited files: {result['edited_files']}")
        print("\nContext management worked - some files were excluded to fit context limit")
    else:
        print(f"Error: {result['error']}")


def demonstrate_agentless_workflow():
    """Demonstrate the complete Agentless workflow."""
    print("\n" + "="*70)
    print("AGENTLESS WORKFLOW DEMONSTRATION")
    print("="*70)
    
    print("1. Multi-file context construction")
    print("2. Single LLM call for all files")
    print("3. Multi-file command parsing")
    print("4. Unified patch generation")
    print("5. Context length management")
    
    generator = AgentlessMultifilePatchGenerator()
    
    # Show context construction
    file_contents = {
        "app.py": "def main():\n    print('Hello')",
        "utils.py": "def helper():\n    return 'help'"
    }
    
    topn_content, intervals = generator.construct_multifile_context(file_contents)
    
    print(f"\nConstructed context:")
    print(topn_content)
    print(f"\nFile intervals: {intervals}")


def main():
    """Run all examples."""
    print("Agentless Multi-file Patch Generator - Usage Examples")
    print("=" * 70)
    
    example_1_calculator_bug_fix()
    example_2_logging_configuration()
    example_3_with_specific_locations()
    example_4_context_management()
    demonstrate_agentless_workflow()
    
    print("\n" + "="*70)
    print("ALL EXAMPLES COMPLETED")
    print("="*70)
    print("\nKey takeaways:")
    print("- Single LLM call processes multiple files simultaneously")
    print("- Context window management automatically handles large codebases")
    print("- File locations allow targeted fixes")
    print("- Unified Git diff output for all changes")
    print("- Complete Agentless workflow simulation")


if __name__ == "__main__":
    main()
