#!/usr/bin/env python3
"""
Test script for agentless_multifile_patch.py

This script demonstrates and tests the Agentless-style multi-file patch generation.
"""

import json
import tempfile
import os
from agentless_multifile_patch import AgentlessMultifilePatchGenerator


def test_basic_multifile_processing():
    """Test basic multi-file processing functionality."""
    print("="*70)
    print("TEST 1: Basic Multi-file Processing")
    print("="*70)
    
    generator = AgentlessMultifilePatchGenerator()
    
    problem = """
    There's a bug in the calculator module where division by zero is not handled properly.
    The main.py file calls the calculator functions but doesn't handle the exceptions.
    Both files need to be fixed to handle division by zero gracefully.
    """
    
    file_contents = {
        "calculator.py": """def add(a, b):
    return a + b

def subtract(a, b):
    return a - b

def divide(a, b):
    return a / b  # Bug: no zero check

def multiply(a, b):
    return a * b""",
        
        "main.py": """from calculator import add, subtract, divide, multiply

def main():
    a, b = 10, 0
    print(f"Addition: {add(a, b)}")
    print(f"Subtraction: {subtract(a, b)}")
    print(f"Division: {divide(a, b)}")  # Bug: will crash
    print(f"Multiplication: {multiply(a, b)}")

if __name__ == "__main__":
    main()"""
    }
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents
    )
    
    print("Problem:", problem.strip())
    print(f"\nFiles to process: {list(file_contents.keys())}")
    print(f"\nResult success: {result['success']}")
    
    if result['success']:
        print(f"Edited files: {result['edited_files']}")
        print("\nGenerated patch:")
        print(result['model_patch'])
    else:
        print(f"Error: {result['error']}")
    
    return result


def test_search_replace_format():
    """Test SEARCH/REPLACE format processing."""
    print("\n" + "="*70)
    print("TEST 2: SEARCH/REPLACE Format")
    print("="*70)
    
    generator = AgentlessMultifilePatchGenerator()
    
    problem = """
    The logging configuration is incorrect in both files.
    config.py has the wrong log level, and main.py doesn't show info messages.
    """
    
    file_contents = {
        "config.py": """import logging

# Bug: wrong log level
logging.basicConfig(level=logging.ERROR)

def get_config():
    return {"debug": True}""",
        
        "utils.py": """import logging

def log_message(msg):
    logging.info(msg)  # Won't show due to ERROR level in config
    
def process_data(data):
    log_message("Processing data")
    return data.upper()"""
    }
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,
        diff_format=True  # Use SEARCH/REPLACE format
    )
    
    print("Problem:", problem.strip())
    print(f"\nFiles to process: {list(file_contents.keys())}")
    print(f"\nResult success: {result['success']}")
    
    if result['success']:
        print(f"Edited files: {result['edited_files']}")
        print("\nGenerated patch:")
        print(result['model_patch'])
    else:
        print(f"Error: {result['error']}")
    
    return result


def test_context_window_management():
    """Test context window and length management."""
    print("\n" + "="*70)
    print("TEST 3: Context Window Management")
    print("="*70)
    
    generator = AgentlessMultifilePatchGenerator(max_context_length=1000)  # Small context for testing
    
    problem = "Fix import statements in all files"
    
    # Create multiple files with substantial content
    file_contents = {}
    for i in range(5):
        file_contents[f"file_{i}.py"] = f"""# File {i}
import os
import sys
import json
import re
import subprocess
import tempfile
import uuid
from collections import OrderedDict
from difflib import unified_diff
from typing import Dict, List, Tuple, Optional, Any

def function_{i}_1():
    '''This is a long function with lots of content to test context management.'''
    data = {{f"key_{{j}}": f"value_{{j}}" for j in range(20)}}
    result = []
    for key, value in data.items():
        if key.startswith("key_"):
            result.append(f"{{key}}: {{value}}")
    return result

def function_{i}_2():
    '''Another function to increase file size.'''
    lines = []
    for k in range(10):
        lines.append(f"Line {{k}} in function_{i}_2")
    return "\\n".join(lines)

class Class_{i}:
    def __init__(self):
        self.data = function_{i}_1()
        self.text = function_{i}_2()
    
    def process(self):
        return len(self.data) + len(self.text)

if __name__ == "__main__":
    obj = Class_{i}()
    print(obj.process())
"""
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents
    )
    
    print("Problem:", problem.strip())
    print(f"\nOriginal files: {len(file_contents)}")
    print(f"Result success: {result['success']}")
    
    if result['success']:
        print(f"Processed files: {len(result.get('edited_files', []))}")
        print(f"Edited files: {result.get('edited_files', [])}")
    else:
        print(f"Error: {result['error']}")
    
    return result


def test_file_locations():
    """Test processing with specific file locations."""
    print("\n" + "="*70)
    print("TEST 4: File Locations")
    print("="*70)
    
    generator = AgentlessMultifilePatchGenerator()
    
    problem = "Fix the specific functions that have bugs"
    
    file_contents = {
        "math_utils.py": """def add(a, b):
    return a + b

def subtract(a, b):
    return a - b

def multiply(a, b):
    return a * b

def divide(a, b):
    return a / b  # Bug: line 10

def power(a, b):
    return a ** b""",
        
        "string_utils.py": """def upper_case(text):
    return text.upper()

def lower_case(text):
    return text.lower()

def reverse_string(text):
    return text[::-1]

def concat_strings(a, b):
    return a + b  # Bug: no separator, line 10

def split_string(text, delimiter):
    return text.split(delimiter)"""
    }
    
    # Specify locations to focus on (the buggy lines)
    file_locations = {
        "math_utils.py": [(10, 10)],      # divide function
        "string_utils.py": [(10, 10)]     # concat_strings function
    }
    
    result = generator.generate_multifile_patch(
        problem_statement=problem,
        file_contents=file_contents,
        file_locations=file_locations,
        context_window=3
    )
    
    print("Problem:", problem.strip())
    print(f"\nFiles with locations: {list(file_locations.keys())}")
    print(f"Result success: {result['success']}")
    
    if result['success']:
        print(f"Edited files: {result['edited_files']}")
        print("\nGenerated patch:")
        print(result['model_patch'])
    else:
        print(f"Error: {result['error']}")
    
    return result


def test_context_construction():
    """Test the context construction functionality."""
    print("\n" + "="*70)
    print("TEST 5: Context Construction")
    print("="*70)
    
    generator = AgentlessMultifilePatchGenerator()
    
    file_contents = {
        "example.py": """line 1
line 2
line 3
line 4
line 5
line 6
line 7
line 8
line 9
line 10""",
        
        "another.py": """function start
some code
more code
important line
final code"""
    }
    
    file_locations = {
        "example.py": [(3, 5)],  # Lines 3-5
        "another.py": [(4, 4)]   # Line 4 only
    }
    
    # Test context construction
    topn_content, file_loc_intervals = generator.construct_multifile_context(
        file_contents, file_locations, context_window=2
    )
    
    print("File contents:")
    for file_path, content in file_contents.items():
        print(f"\n{file_path}:")
        for i, line in enumerate(content.split('\n'), 1):
            print(f"{i:2d}: {line}")
    
    print(f"\nFile locations: {file_locations}")
    print(f"\nConstructed context:")
    print(topn_content)
    print(f"\nFile intervals: {file_loc_intervals}")


def run_all_tests():
    """Run all test functions."""
    print("Running Agentless Multi-file Patch Generator Tests...")
    print("=" * 70)
    
    test_context_construction()
    test_basic_multifile_processing()
    test_search_replace_format()
    test_context_window_management()
    test_file_locations()
    
    print("\n" + "="*70)
    print("ALL TESTS COMPLETED")
    print("="*70)


if __name__ == "__main__":
    run_all_tests()
